import { DataSource } from 'typeorm';
import { TagsIdentity } from '../modules/tags-identity/tags-identity.entity';
import { Logger } from '@nestjs/common';

export const createIdentitySeed = async (dataSource: DataSource) => {
  const repository = dataSource.getRepository(TagsIdentity);

  const defaultValues = [
    {
      name: 'Profesional',
      content:
        'Debe comportarse con un tono formal y proporcionar respuestas claras y concisas. Adecuado para ambientes de negocio serios.',
    },
    {
      name: 'Amigable',
      content:
        'Debe tener un tono más casual y amigable. Debería usar un lenguaje más informal e incluir emojis en sus respuestas.',
    },
    {
      name: 'Divertido',
      content:
        'Debe usar humor en sus respuestas y hacer bromas de vez en cuando. Adecuado para empresas con una imagen de marca relajada y divertida.',
    },
    {
      name: 'Instructivo',
      content:
        'Debe centrarse en proporcionar explicaciones claras y detalladas. Adecuado para productos o servicios que requieran mucha educación o soporte al cliente.',
    },
    {
      name: 'Empático',
      content:
        'Debe mostrar mucha empatía y comprensión en sus respuestas. Adecuado para servicios de atención al cliente o empresas en el sector de la salud.',
    },
    {
      name: 'Innovador',
      content:
        'Debe utilizar lenguaje de vanguardia y estar al tanto de las últimas tendencias. Adecuado para empresas de tecnología o startups.',
    },
    {
      name: 'Asesor',
      content:
        'Debe proporcionar recomendaciones basadas en la información que recopile. Adecuado para tiendas online o servicios personalizados.',
    },
  ];

  for (const value of defaultValues) {
    const exists = await repository.findOne({ where: { name: value.name } });
    if (!exists) {
      await repository.save(value);
      Logger.debug(`Identity Tag ${value.name} created!`);
    }
  }
};
