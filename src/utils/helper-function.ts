import { BadRequestException } from '@nestjs/common';
import * as path from 'path';
import {
  messageStatus,
  MessageStatus,
} from '../modules/chat-response-generator/chat-response-generator.enum';

export function getMessageByMessageType(messageType: messageStatus) {
  switch (messageType) {
    case MessageStatus.SENT:
      return '<PERSON><PERSON>, su solicitud de registro ya fue enviado y notificado a Urban Group, una vez que su solicitud sea aprobada se le enviara un mensaje de confirmación';
    case MessageStatus.PENDING:
      return '<PERSON><PERSON>, ya tienes una solicitud pendiente, una vez que su solicitud sea aprobada se le enviara un mensaje de confirmación.';
    case MessageStatus.INVALID:
      return 'Mensaje Inválido.';
    case MessageStatus.REJECTED:
      return 'Lamentablemente, tu solicitud no ha sido aceptada. Disculpa las molestias.';
    case MessageStatus.ACCEPTED:
      return '¡Gracias por registrarte! Tu solicitud ha sido aceptada, disfruta de nuestros servicios. ¡Bienvenido!';
    default:
      return 'Mensaje Inválido.';
  }
}

export function generateBotSecretKey() {
  const secretKey = Math.random().toString(36).substring(2, 15);

  return secretKey;
}

export const allowedFileTypes = ['.txt'];

export function fileFieldsInterceptor(req, file, cb) {
  const ext = path.extname(file.originalname);

  if (!allowedFileTypes.includes(ext)) {
    return cb(new BadRequestException('Invalid file type'), false);
  }

  cb(null, true);
}

export function parsedChatHistoryInString(chatHistory: any[]) {
  let formattedString = '';
  const chatHistoryCut = chatHistory.slice(-30);

  chatHistoryCut.forEach((entry) => {
    formattedString += `${entry.role}=${entry.content}\n`;
  });

  return formattedString;
}

export function getPeakHourPerDay(data: any) {
  const results = {};

  for (const day in data) {
    let maxHour = null;
    let maxValue = -Infinity;

    for (const hour in data[day]) {
      if (data[day][hour] > maxValue) {
        maxHour = hour;
        maxValue = data[day][hour];
      }
    }

    results[day] = {
      hour: maxHour,
      value: maxValue,
    };
  }

  return results;
}
