import { config as dotenvConfig } from 'dotenv';
import { Logger } from '@nestjs/common';
import { DataSource, DataSourceOptions } from 'typeorm';

import { createIdentitySeed } from '../seeds/identityTags.seeds';

// Load environment variables before using them
dotenvConfig();

// Debug database configuration
console.log('Database Configuration:');
console.log('HOST:', process.env.DATABASE_HOST);
console.log('PORT:', process.env.DATABASE_HOST_PORT);
console.log('DATABASE:', process.env.DATABASE_NAME);
console.log('USERNAME:', process.env.DATABASE_USERNAME);

export const dataSourceOptions: DataSourceOptions = {
  type: 'postgres',
  host: process.env.DATABASE_HOST,
  port: parseInt(process.env.DATABASE_HOST_PORT),
  username: process.env.DATABASE_USERNAME,
  password: process.env.DATABASE_PASSWORD,
  database: process.env.DATABASE_NAME,
  entities: ['dist/**/*.entity.js'],
  schema: 'public',
  synchronize: true,
  ssl: {
    rejectUnauthorized: false,
  },
};

const shouldRunSeeds = process.env.RUN_SEEDS === 'true';

const dataSource = new DataSource(dataSourceOptions);
dataSource.initialize().then(() => {
  Logger.debug('Data Source has been initialized!');

  if (shouldRunSeeds) {
    Logger.debug('Running seeds...');
    const seeds = [createIdentitySeed(dataSource)];

    /** Place to set all the database seeds */
    Promise.all(seeds).then(() => {
      Logger.debug('Seeds executed successfully!');
    });
  }
});
export default dataSource;
