import { Logger } from '@nestjs/common';
import { DataSource, DataSourceOptions } from 'typeorm';

import { createIdentitySeed } from '../seeds/identityTags.seeds';

export const dataSourceOptions: DataSourceOptions = {
  type: 'postgres',
  host: process.env.DATABASE_HOST,
  port: parseInt(process.env.DATABASE_HOST_PORT),
  username: process.env.DATABASE_USERNAME,
  password: process.env.DATABASE_PASSWORD,
  database: process.env.DATABASE_NAME,
  entities: ['dist/**/*.entity.js'],
  schema: 'public',
  synchronize: true,
  ssl: {
    rejectUnauthorized: false,
  },
};

const shouldRunSeeds = process.env.RUN_SEEDS === 'true';

const dataSource = new DataSource(dataSourceOptions);
dataSource.initialize().then(() => {
  Logger.debug('Data Source has been initialized!');

  if (shouldRunSeeds) {
    Logger.debug('Running seeds...');
    const seeds = [createIdentitySeed(dataSource)];

    /** Place to set all the database seeds */
    Promise.all(seeds).then(() => {
      Logger.debug('Seeds executed successfully!');
    });
  }
});
export default dataSource;
