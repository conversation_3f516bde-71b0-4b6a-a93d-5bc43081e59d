import { ValidationPipe } from '@nestjs/common';
import { CorsOptions } from '@nestjs/common/interfaces/external/cors-options.interface';
import { NestFactory } from '@nestjs/core';
import * as cookieParser from 'cookie-parser';

import { AppModule } from './app.module';

async function bootstrap() {
  const app = await NestFactory.create(AppModule);
  app.use(cookieParser());

  const corsOptions: CorsOptions = {
    origin: 'https://app.biitbot.com', // <- Reemplaza con el dominio *exacto* de tu frontend
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'], // <- <PERSON><PERSON>ye todos los métodos que uses
    // credentials: true, // <- Si usas cookies o autenticación basada en credenciales, ponlo a true. Si no, ponlo a false o elimínalo
  };

  app.enableCors();

  app.useGlobalPipes(
    new ValidationPipe({
      transform: true,
      enableDebugMessages: true,
    }),
  );

  const port = process.env.PORT || 3000;
  console.log(`Listening on port ${port}`);

  await app.listen(port);
}

bootstrap();
