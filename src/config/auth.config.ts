import { registerAs } from '@nestjs/config';

export interface AuthConfig {
  jwtSecret: string;
  accessTokenExpiresIn: string;
  refreshTokenExpiresIn: string;
  emailTokenExpiresIn: string;
  passwordResetTokenExpiresIn: string;
  frontendUrl: string;
  baseDomain: string;
  redis: {
    host: string;
    port: number;
    password?: string;
    db: number;
  };
}

export default registerAs('auth', (): AuthConfig => {
  // Validate required environment variables
  const requiredEnvVars = ['JWT_SECRET_KEY', 'FRONT_END_URL', 'BASE_DOMAIN'];

  for (const envVar of requiredEnvVars) {
    if (!process.env[envVar]) {
      throw new Error(`Missing required environment variable: ${envVar}`);
    }
  }

  return {
    jwtSecret: process.env.JWT_SECRET_KEY,
    accessTokenExpiresIn: process.env.ACCESS_TOKEN_EXPIRES_IN || '30m', // 30 minutes default
    refreshTokenExpiresIn: process.env.REFRESH_TOKEN_EXPIRES_IN || '7d', // 7 days default
    emailTokenExpiresIn: process.env.EMAIL_TOKEN_EXPIRES_IN || '24h', // 24 hours default
    passwordResetTokenExpiresIn:
      process.env.PASSWORD_RESET_TOKEN_EXPIRES_IN || '1h', // 1 hour default
    frontendUrl: process.env.FRONT_END_URL,
    baseDomain: process.env.BASE_DOMAIN,
    redis: {
      host: process.env.REDIS_HOST || 'localhost',
      port: parseInt(process.env.REDIS_PORT) || 6379,
      password: process.env.REDIS_PASSWORD,
      db: parseInt(process.env.REDIS_DB) || 0,
    },
  };
});
