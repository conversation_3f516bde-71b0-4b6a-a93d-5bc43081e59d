import { <PERSON><PERSON><PERSON>y, IsDecimal, Is<PERSON>num, IsString } from 'class-validator';
import { ORDER_STATUS } from './order.interface';
import { Transform } from 'class-transformer';

export class UpdateOrderStatusDto {
  @IsEnum(ORDER_STATUS)
  status: ORDER_STATUS;
}

interface Product {
  id: string;
  quantity: number;
  name?: string;
}

export class CreateOrderDto {
  @IsArray()
  products: Product[];
}

export class UpdateOrderDto extends CreateOrderDto {
  @IsString()
  id: string;
}
