import {
  Before<PERSON><PERSON>rt,
  <PERSON>umn,
  CreateDateColumn,
  Entity,
  JoinColumn,
  ManyToOne,
  OneToMany,
  OneToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { ORDER_STATUS } from './order.interface';
import { Company } from '../companies/companies.entity';
import { OrderNumberEntity } from './orderNumber.entity';
import { OrderItemEntity } from './order-item.entity';

@Entity()
export class OrderEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ enum: ORDER_STATUS, default: ORDER_STATUS.PENDING })
  status: ORDER_STATUS;

  @Column('decimal', { precision: 10, scale: 2 })
  totalAmount: number;

  @OneToMany(() => OrderItemEntity, (item) => item.order, {
    cascade: true,
    eager: true,
  })
  orderItems: OrderItemEntity[];

  // Propiedad virtual para acceder a los productos
  get products() {
    return this.orderItems?.map(item => ({
      ...item.product,
      quantity: item.quantity,
      priceAtOrder: item.priceAtOrder,
      total: item.total
    })) || [];
  }

  @OneToOne(() => OrderNumberEntity, (orderNumber) => orderNumber.order, {
    onDelete: 'SET NULL',
    eager: true,
  })
  @JoinColumn()
  orderNumber: OrderNumberEntity;

  @ManyToOne(() => Company, (company) => company.orders, { eager: true })
  @JoinColumn()
  company: Company;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
