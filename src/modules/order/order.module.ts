import { <PERSON>du<PERSON> } from '@nestjs/common';
import { OrderController } from './order.controller';
import { OrderService } from './order.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import { OrderEntity } from './order.entity';
import { OrderItemEntity } from './order-item.entity';
import { OrderNumberEntity } from './orderNumber.entity';
import { CompaniesModule } from '../companies/companies.module';
import { ProductModule } from '../product/product.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      OrderEntity, 
      OrderItemEntity, 
      OrderNumberEntity
    ]),
    CompaniesModule,
    ProductModule,
  ],
  controllers: [OrderController],
  providers: [OrderService],
  exports: [OrderService],
})
export class OrderModule {}
