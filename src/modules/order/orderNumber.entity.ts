import moment = require('moment');
import {
  Before<PERSON><PERSON>rt,
  Column,
  CreateDateColumn,
  Entity,
  OneToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { OrderEntity } from './order.entity';

@Entity()
export class OrderNumberEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ unique: true })
  orderNumber: number;

  @Column({ default: Number(moment().format('YY')) })
  baseId: number;

  @OneToOne(() => OrderEntity, (orderEntity) => orderEntity.orderNumber)
  order: OrderEntity;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
