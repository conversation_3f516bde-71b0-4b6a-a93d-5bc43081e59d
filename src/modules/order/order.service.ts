import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { OrderEntity } from './order.entity';
import { CreateOrderDto, UpdateOrderDto } from './order.dto';
import { CompaniesService } from '../companies/companies.service';
import { ProductService } from '../product/product.service';
import { OrderNumberEntity } from './orderNumber.entity';
import { ORDER_STATUS } from './order.interface';
import { OrderItemEntity } from './order-item.entity';

@Injectable()
export class OrderService {
  constructor(
    @InjectRepository(OrderEntity)
    private readonly orderEntity: Repository<OrderEntity>,
    @InjectRepository(OrderNumberEntity)
    private readonly orderNumberEntity: Repository<OrderNumberEntity>,
    @InjectRepository(OrderItemEntity)
    private readonly orderItemRepository: Repository<OrderItemEntity>,
    private readonly companyService: CompaniesService,
    private readonly productService: ProductService,
  ) {}

  async getOrders(userId: number) {
    return this.orderEntity.find({
      where: { company: { employees: { id: userId } } },
    });
  }

  async getOrdersByOrderNumber(orderNumber: string) {
    const orderNumberPattern = /^\d{2}-\d+$/;
    if (!orderNumberPattern.test(orderNumber)) {
      throw new Error(
        'Invalid order number format. Expected format: number-number',
      );
    }

    const [baseId, orderNumberToUse] = orderNumber.split('-');

    return this.orderEntity.findOne({
      where: {
        orderNumber: {
          baseId: Number(baseId),
          orderNumber: Number(orderNumberToUse),
        },
      },
    });
  }

  async getOrder(orderId: string) {
    return this.orderEntity.findOne({
      where: { id: orderId },
      relations: ['orderItems', 'orderItems.product']
    });
  }

  async updateOrderStatus(orderId: string, status: ORDER_STATUS) {
    const order = await this.orderEntity.findOne({ where: { id: orderId } });

    if (!order) {
      throw new NotFoundException('order not exist');
    }

    order.status = status;
    await this.orderEntity.save(order);

    return order;
  }

  async deleteOrderByOrderId(orderId: string) {
    return this.orderEntity.delete({ id: orderId });
  }

  async createOrder(companyId: number, order: CreateOrderDto) {
    const company = await this.companyService.getCompanyByCompanyId(companyId);

    if (!company) {
      throw new NotFoundException('company not exist');
    }

    // Obtener todos los IDs de productos únicos para validar su existencia
    const uniqueProductIds = [...new Set(order.products.map(p => p.id))];
    const products = await this.productService.getProductsByIds(uniqueProductIds);
    
    // Validar que todos los productos existan
    if (products.length !== uniqueProductIds.length) {
      const foundIds = products.map(p => p.id);
      const missingIds = uniqueProductIds.filter(id => !foundIds.includes(id));
      throw new NotFoundException(`Los siguientes productos no existen: ${missingIds.join(', ')}`);
    }

    // Crear la orden
    const orderNumber = await this.generateOrderId();
    const newOrder = new OrderEntity();
    newOrder.company = company;
    
    // Crear los order items para cada producto en la orden (incluyendo duplicados)
    newOrder.orderItems = [];
    
    for (const orderProduct of order.products) {
      const product = products.find(p => p.id === orderProduct.id);
      if (!product) continue; // Esto no debería pasar ya que validamos antes
      
      const quantity = orderProduct.quantity || 1;
      const price = product.price;
      const total = price * quantity;
      
      const orderItem = new OrderItemEntity();
      orderItem.product = product;
      orderItem.quantity = quantity;
      orderItem.priceAtOrder = price;
      orderItem.total = total;
      
      newOrder.orderItems.push(orderItem);
    }
    
    // Calcular el total de la orden
    newOrder.totalAmount = newOrder.orderItems.reduce((sum, item) => sum + item.total, 0);
    newOrder.company = company;
    newOrder.status = ORDER_STATUS.PENDING;
    newOrder.orderNumber = orderNumber;
    await this.orderEntity.save(newOrder);

    return newOrder;
  }

  async updateOrder(companyId: number, orderData: UpdateOrderDto) {
    // Verificar que la orden exista y pertenezca a la compañía
    const existingOrder = await this.orderEntity.findOne({
      where: { 
        id: orderData.id,
        company: { id: companyId } 
      },
      relations: ['company', 'orderItems', 'orderItems.product']
    });

    if (!existingOrder) {
      throw new NotFoundException('Order not found or does not belong to company');
    }

    // Obtener los productos actualizados
    const productIds = orderData.products.map(p => p.id);
    const products = await this.productService.getProductsByIds(productIds);
    
    // Validar que todos los productos existan
    if (products.length !== productIds.length) {
      const foundIds = products.map(p => p.id);
      const missingIds = productIds.filter(id => !foundIds.includes(id));
      throw new NotFoundException(`Los siguientes productos no existen: ${missingIds.join(', ')}`);
    }

    // Eliminar order items existentes
    if (existingOrder.orderItems && existingOrder.orderItems.length > 0) {
      await this.orderItemRepository.remove(existingOrder.orderItems);
    }

    // Crear nuevos order items
    const orderItems = orderData.products.map(orderProduct => {
      const product = products.find(p => p.id === orderProduct.id);
      if (!product) return null;
      
      const quantity = orderProduct.quantity || 1;
      const price = product.price;
      const total = price * quantity;
      
      const orderItem = new OrderItemEntity();
      orderItem.product = product;
      orderItem.quantity = quantity;
      orderItem.priceAtOrder = price;
      orderItem.total = total;
      
      return orderItem;
    }).filter(Boolean);

    // Actualizar la orden
    existingOrder.orderItems = orderItems;
    existingOrder.totalAmount = orderItems.reduce((sum, item) => sum + item.total, 0);
    existingOrder.updatedAt = new Date();

    // Guardar los cambios
    await this.orderEntity.save(existingOrder);

    // Devolver la orden actualizada con las relaciones
    return this.orderEntity.findOne({
      where: { id: orderData.id },
      relations: ['orderItems', 'orderItems.product', 'orderNumber', 'company']
    });
  }

  async generateOrderId(): Promise<OrderNumberEntity> {
    const lastOrderNumberGenerated = await this.orderNumberEntity.findOne({
      where: {},
      order: { createdAt: 'DESC' },
    });

    const newOrderNumber = new OrderNumberEntity();
    newOrderNumber.orderNumber = lastOrderNumberGenerated
      ? lastOrderNumberGenerated.orderNumber + 1
      : 1;

    return this.orderNumberEntity.save(newOrderNumber);
  }

  async getOrderFunctionCalling() {
    const getOrderStatus = {
      type: 'function',
      function: {
        name: 'get_order_status',
        description:
          'Retrieve the status of an order. Call this function if the user wants to know their order status.',
        parameters: {
          type: 'object',
          required: ['order_number'],
          properties: {
            order_number: {
              type: 'string',
              description:
                'The unique order number to get details for and the format should be two digits, a hyphen, followed by one or more digits (e.g., 25-1). If the provided order number does not match this format, inform the user that the order number is invalid and provide an example of the correct format: "25-1".',
              pattern: '^\\d{2}-\\d+$',
            },
          },
          additionalProperties: false,
        },
      },
    };

    const createOrder = {
      type: 'function',
      function: {
        name: 'create_order',
        description:
          "Create a new order based on the user's specified items and quantities. Before calling this function, ask the user to confirm their order details. Should call get_menu function to get the list of products and the product id.",
        parameters: {
          type: 'object',
          required: ['products'],
          properties: {
            products: {
              type: 'array',
              description: 'List of products to be ordered',
              items: {
                type: 'object',
                required: ['id', 'quantity'],
                properties: {
                  id: {
                    type: 'string',
                    description: 'use id property from get_menu function',
                  },
                  quantity: {
                    type: 'number',
                    description: 'The quantity of the product to be ordered',
                  },
                },
                additionalProperties: false,
              },
            },
          },
          additionalProperties: false,
        },
      },
    };

    const getMenu = {
      type: 'function',
      function: {
        name: 'get_menu',
        description:
          'Retrieve the menu or details about a specific product. Call this function if the user expresses interest in ordering or wants to know more about the available items.',
        parameters: {
          type: 'object',
          additionalProperties: false,
          properties: {
            product_name: {
              type: 'string',
              description:
                'The name of the product to get details for. If not provided, the entire menu will be returned.',
            },
          },
          required: [],
        },
      },
    };

    const cancelOrder = {
      type: 'function',
      function: {
        name: 'cancel_order',
        description:
          'Call this function if the user wants to cancel an order and has already provided the order number. This function requires the order number to proceed with cancellation.',
        parameters: {
          type: 'object',
          required: ['order_number'],
          properties: {
            order_number: {
              type: 'string',
              description:
                'The order number to cancel. The format should be two digits, a hyphen, followed by one or more digits (e.g., 25-1). If the provided order number does not match this format, inform the user that the order number is invalid and provide an example of the correct format: "25-1".',
              pattern: '^\\d{2}-\\d+$',
            },
          },
          additionalProperties: false,
        },
      },
    };

    return [getOrderStatus, getMenu, createOrder, cancelOrder];
  }
}
