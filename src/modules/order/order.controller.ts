import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Put,
  Req,
  UseGuards,
} from '@nestjs/common';

import { CreateOrderDto, UpdateOrderStatusDto, UpdateOrderDto } from './order.dto';
import { OrderService } from './order.service';
import { AuthGuard } from '../auth/auth.guard';

@UseGuards(AuthGuard)
@Controller('order')
export class OrderController {
  constructor(private orderService: OrderService) {}

  @Post()
  async createOrder(@Body() body: CreateOrderDto, @Req() req: any) {
    return this.orderService.createOrder(req.user.company.id, body);
  }

  @Get()
  async getOrders(@Req() req: any) {
    return this.orderService.getOrders(Number(req.user.sub));
  }

  @Get(':id')
  async getOrder(@Param('id') orderId: string) {
    return this.orderService.getOrder(orderId);
  }

  @Put('status/:id')
  async updateOrderStatus(
    @Param('id') orderId: string,
    @Body() body: UpdateOrderStatusDto,
  ) {
    return this.orderService.updateOrderStatus(orderId, body.status);
  }

  @Put()
  async updateOrder(
    @Body() body: UpdateOrderDto,
    @Req() req: any
  ) {
    return this.orderService.updateOrder(req.user.company.id, body);
  }

  @Delete(':id')
  async deleteOrder(@Param('id') orderId) {
    return this.orderService.deleteOrderByOrderId(orderId);
  }
}
