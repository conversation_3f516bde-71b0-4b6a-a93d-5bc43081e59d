import {
  Body,
  Controller,
  Get,
  Post,
  Put,
  Req,
  UseGuards,
} from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';

import { <PERSON><PERSON>, CrudController } from '@nestjsx/crud';
import { AuthGuard } from '../auth/auth.guard';
import { AddEmployeeDto } from './companies.dto';
import { Company } from './companies.entity';
import { CompaniesService } from './companies.service';

@UseGuards(AuthGuard)
@Crud({
  model: {
    type: Company,
  },
  query: {
    join: {
      employees: {
        eager: true,
      },
    },
  },
})
@ApiTags('companies')
@Controller('companies')
export class CompaniesController implements CrudController<Company> {
  constructor(public service: CompaniesService) {}

  @Post('add/employee')
  async addEmployee(@Body() body: AddEmployeeDto) {
    return this.service.addEmployee(body.companyId, body.employeeId);
  }

  @Put()
  async updateCompany(@Body() body: any, @Req() req: any) {
    return this.service.updateCompanyByUserAuthId(req.user.sub, body);
  }

  @Get()
  async getCompany(@Req() req: any) {
    return this.service.getCompanyByUserAuthId(req.user.sub);
  }

  @Get('bot/web/history')
  async getBotWebHistory(@Req() req: any) {
    return this.service.getBotWebHistoryByUserAuthId(req.user.sub);
  }
}
