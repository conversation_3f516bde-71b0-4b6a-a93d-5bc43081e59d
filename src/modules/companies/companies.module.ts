import { Module, forwardRef } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

import { Company } from './companies.entity';
import { CompaniesService } from './companies.service';
import { CompaniesController } from './companies.controller';
import { EmployeeModule } from '../employee/employee.module';
import { BotCompanyModule } from '../bot-company/bot-company.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([Company]),
    forwardRef(() => EmployeeModule),
    forwardRef(() => BotCompanyModule),
  ],
  providers: [CompaniesService],
  controllers: [CompaniesController],
  exports: [CompaniesService, CompaniesModule],
})
export class CompaniesModule {}
