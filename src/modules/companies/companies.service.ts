import { Inject, Injectable, forwardRef } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { TypeOrmCrudService } from '@nestjsx/crud-typeorm';

import { Company } from './companies.entity';
import { UpdateCompanyDto } from './companies.dto';
import { EmployeeService } from '../employee/employee.service';
import { BotCompanyService } from '../bot-company/bot-company.service';
import { BotCompany } from '../bot-company/bot-company.entity';

@Injectable()
export class CompaniesService extends TypeOrmCrudService<Company> {
  constructor(
    @InjectRepository(Company) repo: any,
    @Inject(forwardRef(() => EmployeeService))
    private employeeService: EmployeeService,
    @Inject(forwardRef(() => BotCompanyService))
    private botCompanyService: BotCompanyService,
  ) {
    super(repo);
  }

  async addEmployee(companyId: number, employeeId: number) {
    const employee = await this.employeeService.getOneEmployee(employeeId);
    const company = await this.getCompanyByCompanyId(companyId);

    company.employees.push(employee);
    return this.repo.save(company);
  }

  async getCompanyByCompanyId(companyId: number) {
    const res = await this.repo.findOne({
      where: { id: companyId },
      relations: ['botCompany'],
    });
    return res;
  }

  async getBotWebHistoryByUserAuthId(userAuthId: number) {
    const company = await this.getCompanyByUserAuthId(userAuthId);
    const botCompany: BotCompany[] = company.botCompany;
    const botCompanyWithHistory =
      await this.botCompanyService.getBotCompanyHistoryByBotRequestId(
        botCompany[0].botRequestId,
      );
    return botCompanyWithHistory;
  }

  async getCompanyByUserAuthId(userAuthId: number) {
    const res = await this.repo.findOne({
      where: { employees: { auth: { id: userAuthId } } },
      relations: ['botCompany'],
    });
    return res;
  }

  async getCompanyByBotRequestId(botRequestId: string): Promise<Company> {
    const botCompany = await this.botCompanyService.getBotCompanyByBotRequestId(
      botRequestId,
    );

    return botCompany?.company;
  }

  async updateCompanyByUserAuthId(userAuthId: number, data: UpdateCompanyDto) {
    const company = await this.getCompanyByUserAuthId(userAuthId);
    company.phoneNumberVerified = true;
    company.requireApproval = false;
    company.phoneNumber = data.phoneNumber;
    company.name = data.name;
    company.address = data.address;
    company.companyDescription = data.companyDescription;
    company.companyPrincipalActivity = data.companyPrincipalActivity;
    company.country = data.country;
    company.email = data.email;
    company.websiteUrl = data.websiteUrl;
    company.companyPrincipalActivity = data.companyPrincipalActivity;

    console.log(data.companyPrincipalActivity);

    return this.repo.save(company);
  }

  async getCompanyByEmployeeId(employeeId: number) {
    return this.repo.findOne({
      where: { employees: { id: employeeId } },
    });
  }

  async getCompanyBySessionId(sessionId: string) {
    return this.repo.findOne({
      where: { botCompany: { chatSession: { clientId: sessionId } } },
    });
  }
}
