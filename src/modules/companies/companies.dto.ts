import { ApiProperty } from '@nestjsx/crud/lib/crud';
import { IsOptional, IsString } from 'class-validator';

import { CreateEmployeeDto } from '../employee/employee.dto';

export class CreateCompanyDto {
  @ApiProperty()
  @IsOptional()
  @IsString()
  name?: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  phoneNumber?: string;

  @ApiProperty()
  @IsOptional()
  phoneNumberVerified?: boolean;

  @ApiProperty()
  @IsOptional()
  requireApproval?: boolean;

  @ApiProperty()
  @IsOptional()
  @IsString()
  companyDescription?: string;

  @ApiProperty()
  @IsOptional()
  websiteUrl?: string;

  @ApiProperty()
  @IsOptional()
  address?: string;

  @ApiProperty()
  @IsOptional()
  companyPrincipalActivity?: string;

  @ApiProperty()
  @IsOptional()
  country?: string;

  @ApiProperty()
  @IsOptional()
  email?: string;
}

export class AddEmployeeDto {
  employeeId: number;
  companyId: number;
}

export class UpdateCompanyDto {
  @IsOptional()
  @IsString()
  name?: string;

  @IsOptional()
  @IsString()
  phoneNumber?: string;

  @IsOptional()
  phoneNumberVerified?: boolean;

  @IsOptional()
  requireApproval?: boolean;

  @IsOptional()
  @IsString()
  companyDescription?: string;

  @IsOptional()
  websiteUrl?: string;

  @IsOptional()
  address?: string;

  @IsOptional()
  companyPrincipalActivity?: string;

  @IsOptional()
  country?: string;

  @IsOptional()
  email?: string;
}
