import {
  <PERSON><PERSON><PERSON>,
  Column,
  PrimaryGeneratedColumn,
  OneToMany,
  JoinColumn,
  CreateDateColumn,
  UpdateDateColumn,
  OneToOne,
  ManyToMany,
} from 'typeorm';

import { BotCompany } from '../bot-company/bot-company.entity';
import { Employee } from './../employee/employee.entity';
import { DataSources } from '../data-sources/data-sources.entity';
import { Teams } from '../team-management/team-management.entity';
import { LeadCenter } from '../lead-center/lead-center.entity';
import { SocialAuth } from '../social-auth/social-auth.entity';
import { Subscription } from '../subscription/subscription.entity';
import { OrderEntity } from '../order/order.entity';
import { ProductEntity } from '../product/product.entity';

@Entity()
export class Company {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ nullable: true })
  name: string;

  @Column({ nullable: true })
  phoneNumber: string;

  @Column({ default: false })
  phoneNumberVerified: boolean;

  @Column({ default: true })
  requireApproval: boolean;

  @Column({ nullable: true })
  websiteUrl: string;

  @Column({ nullable: true })
  address: string;

  @Column({ nullable: true })
  companyDescription: string;

  @Column({ nullable: true })
  companyPrincipalActivity: string;

  @Column({ nullable: true })
  country: string;

  @Column({ nullable: true })
  email: string;

  @OneToMany(() => Employee, (employee) => employee.company, {
    nullable: false,
  })
  @JoinColumn()
  employees: Employee[];

  @OneToMany(() => Teams, (teams) => teams.company)
  @JoinColumn()
  teams: Teams[];

  @OneToMany(() => BotCompany, (botCompany) => botCompany.company)
  @JoinColumn()
  botCompany: BotCompany[];

  @OneToMany(() => DataSources, (dataSources) => dataSources.company)
  @JoinColumn()
  dataSources: DataSources[];

  @OneToOne(() => LeadCenter, (leadCenter) => leadCenter.company)
  leadCenter: LeadCenter;

  @OneToOne(() => SocialAuth, (socialAuth) => socialAuth.company)
  socialAuth: SocialAuth[];

  @OneToOne(() => Subscription, (subscription) => subscription.company, {
    onDelete: 'SET NULL',
    eager: true,
  })
  @JoinColumn()
  subscription: Subscription;

  @OneToMany(() => OrderEntity, (order) => order.company, {
    onDelete: 'CASCADE',
  })
  orders: OrderEntity[];

  @OneToMany(() => ProductEntity, (product) => product.company)
  products: ProductEntity[];

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
