import {
  Controller,
  Get,
  Param,
  Put,
  Req,
  UseGuards,
  Body,
  Delete,
  Post,
} from '@nestjs/common';
import { AuthGuard } from '../auth/auth.guard';
import { DataSourcesService } from './data-sources.service';
import {
  CreateDataSourceDto,
  DeleteDataSourceDto,
  UpdateDataSourceDto,
} from './data-sources.dto';

@UseGuards(AuthGuard)
@Controller('data-sources')
export class DataSourcesController {
  constructor(private dataSourcesService: DataSourcesService) {}

  @Get()
  async getDataSources(@Req() req) {
    const employeeId = req.user.sub;
    return this.dataSourcesService.getDataSourcesByEmployeeId(employeeId);
  }

  @Post()
  async createDataSource(@Req() req, @Body() body: CreateDataSourceDto) {
    const employeeId = req.user.sub;
    return this.dataSourcesService.createDataSource(employeeId, body);
  }

  @Put()
  async updateDataSources(@Req() req, @Body() body: UpdateDataSourceDto) {
    return this.dataSourcesService.updateDataSourcesByDataSourceId(
      req.user.sub,
      body,
    );
  }

  @Delete(':id')
  async deleteDataSource(@Req() req, @Param() param) {
    console.log(param);
    return this.dataSourcesService.deleteDataSourceByDataSourceId(
      req.user.sub,
      param.id,
    );
  }
}
