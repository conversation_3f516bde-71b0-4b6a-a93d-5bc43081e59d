import {
  Column,
  CreateDateColumn,
  Entity,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { BotCompany } from '../bot-company/bot-company.entity';
import { Company } from '../companies/companies.entity';

@Entity()
export class DataSources {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ nullable: false, type: 'text', default: 'Untitled' })
  name: string;

  @Column({ nullable: false, type: 'text' })
  content: string;

  @OneToMany(() => BotCompany, (botCompany) => botCompany.dataSource)
  botCompany: BotCompany[];

  @ManyToOne(() => Company, (company) => company.dataSources)
  company: Company;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
