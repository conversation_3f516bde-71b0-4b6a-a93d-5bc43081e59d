import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ForbiddenException } from '@nestjs/common';

import { DataSources } from './data-sources.entity';
import { Company } from '../companies/companies.entity';
import { CreateDataSourceDto, UpdateDataSourceDto } from './data-sources.dto';

@Injectable()
export class DataSourcesService {
  constructor(
    @InjectRepository(DataSources)
    private dataSourceRepository: Repository<DataSources>,
    @InjectRepository(Company)
    private companyRepository: Repository<Company>,
  ) {}

  async getDataSourcesByEmployeeId(employeeId: number) {
    const dataSources = await this.dataSourceRepository.find({
      where: {
        company: {
          employees: { id: employeeId },
        },
      },
    });
    return dataSources;
  }

  async updateDataSourcesByDataSourceId(
    employeeId: number,
    dataSource: UpdateDataSourceDto,
  ) {
    const currentDataSource = await this.dataSourceRepository.findOne({
      where: {
        id: dataSource.id,
        company: {
          employees: { id: employeeId },
        },
      },
    });
    currentDataSource.name = dataSource.name;
    currentDataSource.content = dataSource.content;

    await this.dataSourceRepository.save(currentDataSource);
    return currentDataSource;
  }

  async deleteDataSourceByDataSourceId(
    employeeId: string,
    dataSourceId: number,
  ) {
    const dataSource = await this.dataSourceRepository.findOne({
      where: {
        id: dataSourceId,
      },
      relations: ['botCompany'],
    });

    if (dataSource.botCompany.length > 0) {
      const messageError = {
        message: 'Data source is in use',
      };
      throw new ForbiddenException(messageError);
    }

    await this.dataSourceRepository.delete(dataSourceId);
  }

  async createDataSource(employeeId: number, body: CreateDataSourceDto) {
    const company = await this.companyRepository.findOne({
      where: {
        employees: { id: employeeId },
      },
    });

    const dataSource = new DataSources();
    dataSource.name = body.name;
    dataSource.content = body.content;
    dataSource.company = company;

    await this.dataSourceRepository.save(dataSource);
    return dataSource;
  }
}
