import { Module } from '@nestjs/common';
import { DataSourcesService } from './data-sources.service';
import { DataSourcesController } from './data-sources.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { DataSources } from './data-sources.entity';
import { Company } from '../companies/companies.entity';

@Module({
  imports: [TypeOrmModule.forFeature([DataSources, Company])],
  providers: [DataSourcesService],
  controllers: [DataSourcesController],
  exports: [DataSourcesService],
})
export class DataSourcesModule {}
