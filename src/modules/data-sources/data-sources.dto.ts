import { Transform } from 'class-transformer';
import { IsNumber, IsString } from 'class-validator';

export class UpdateDataSourceDto {
  @IsNumber()
  @Transform(({ value }) => Number(value))
  id: number;

  @IsString()
  content: string;

  @IsString()
  name: string;
}

export class DeleteDataSourceDto {
  @IsNumber()
  @Transform(({ value }) => Number(value))
  dataSourceId: number;
}

export class CreateDataSourceDto {
  @IsString()
  name: string;

  @IsString()
  content: string;
}
