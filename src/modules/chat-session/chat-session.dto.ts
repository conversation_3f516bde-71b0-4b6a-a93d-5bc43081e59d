import { Transform } from 'class-transformer';
import { IsNumber, IsString } from 'class-validator';

export class GetChatHistoryByBotRequestIdDto {
  @IsString()
  botRequestId: string;
}

export class GetChatHistoryByChatSessionIdDto {
  @IsString()
  sessionId: string;
}

export class DeleteChatSessionDto {
  @IsString()
  sessionId: string;
}

export class ToggleResponseDto {
  @IsString()
  sessionId: string;
}

export class AssignChatToTeamDto {
  @IsNumber()
  @Transform(({ value }) => Number(value))
  chatSessionId: number;

  @IsNumber()
  @Transform(({ value }) => Number(value))
  teamId: number;
}
