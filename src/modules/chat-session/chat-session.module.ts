import { Module, forwardRef } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ChatResponseGeneratorModule } from '../chat-response-generator/chat-response-generator.module';
import { ChatSession } from './chat-session.entity';
import { ChatSessionService } from './chat-session.service';
import { ChatSessionController } from './chat-session.controller';
import { BotCompany } from '../bot-company/bot-company.entity';
import { OpenAiModule } from '../open-ai/open-ai.module';
import { Teams } from '../team-management/team-management.entity';
import { EmployeeModule } from '../employee/employee.module';
import { WhatsappModule } from '../whatsapp/whatsapp.module';
import { ContactModule } from '../contact/contact.module';
import { FacebookModule } from '../facebook/facebook.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([ChatSession, BotCompany, Teams]),
    forwardRef(() => ChatResponseGeneratorModule),
    forwardRef(() => OpenAiModule),
    forwardRef(() => EmployeeModule),
    forwardRef(() => WhatsappModule),
    forwardRef(() => ContactModule),
    forwardRef(() => FacebookModule),
  ],
  providers: [ChatSessionService],
  exports: [ChatSessionService],
  controllers: [ChatSessionController],
})
export class ChatSessionModule {}
