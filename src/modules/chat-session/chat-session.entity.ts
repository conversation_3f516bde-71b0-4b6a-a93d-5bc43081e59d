import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  <PERSON>in<PERSON><PERSON><PERSON><PERSON>,
  OneToMany,
  OneToOne,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
} from 'typeorm';

import { MessageEntryPoint } from '../message-entry-point/message-entry-point.entity';
import { ChatResponseGenerator } from '../chat-response-generator/chat-response-generator.entity';
import { BotCompany } from '../bot-company/bot-company.entity';
import { Teams } from '../team-management/team-management.entity';
import { WhatsappChat } from '../whatsapp/whatsapp-chat.entity';
import { Task } from '../lead-center/task.entity';
import { Contact } from '../contact/contact.entity';
import { FacebookMessages } from '../facebook/facebook.entity';

@Entity()
export class ChatSession {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  clientId: string;

  @OneToOne(
    () => MessageEntryPoint,
    (messageEntryPoint) => messageEntryPoint.chatSession,
  )
  messageEntryPoint: MessageEntryPoint;

  @ManyToOne(() => BotCompany, (botCompany) => botCompany.chatSession)
  @JoinColumn()
  botCompany: BotCompany;

  @OneToMany(
    () => ChatResponseGenerator,
    (chatResponseGenerator) => chatResponseGenerator.chatSession,
    { onDelete: 'CASCADE' },
  )
  @JoinColumn()
  chatResponseGenerator: ChatResponseGenerator[];

  @OneToOne(() => WhatsappChat, (whatsappChat) => whatsappChat.chatSession, {
    onDelete: 'CASCADE',
  })
  whatsappChat: WhatsappChat;

  @OneToOne(
    () => FacebookMessages,
    (facebookMessages) => facebookMessages.chatSession,
    {
      onDelete: 'CASCADE',
    },
  )
  facebookChat: FacebookMessages;

  @ManyToOne(() => Teams, (teams) => teams.chatSession, { eager: true })
  @JoinColumn()
  team: Teams;

  @Column({ nullable: false, default: true })
  shouldAiResponse: boolean;

  @OneToOne(() => Task, (task) => task.chatSession)
  task: Task;

  @Column({ type: 'boolean', default: false })
  potentialClient: boolean;

  @Column({ nullable: true, type: 'simple-array' })
  tags: string[];

  @OneToOne(() => Contact, (contact) => contact.chatSession, {
    onDelete: 'SET NULL',
    eager: true,
  })
  contact: Contact;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
