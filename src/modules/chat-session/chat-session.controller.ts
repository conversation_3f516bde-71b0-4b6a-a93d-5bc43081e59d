import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  ParseIntPipe,
  Post,
  Req,
  UseGuards,
} from '@nestjs/common';
import { ChatSessionService } from './chat-session.service';
import { AuthGuard } from '../auth/auth.guard';
import {
  AssignChatToTeamDto,
  DeleteChatSessionDto,
  GetChatHistoryByChatSessionIdDto,
  ToggleResponseDto,
} from './chat-session.dto';

@UseGuards(AuthGuard)
@Controller('chat-session')
export class ChatSessionController {
  constructor(private chatSessionService: ChatSessionService) {}

  @Get()
  async getChatSessions(@Req() req) {
    return this.chatSessionService.getChatSessionByEmployeeId(req.user.sub);
  }

  @Get('history')
  async getChatHistory(@Body() body: GetChatHistoryByChatSessionIdDto) {
    return this.chatSessionService.getChatHistoryByChatSessionId(
      body.sessionId,
    );
  }

  @Delete(':sessionId')
  async deleteChatSession(@Param() param: DeleteChatSessionDto) {
    return this.chatSessionService.deleteChatSessionBySessionId(
      param.sessionId,
    );
  }

  @Post('toggle-ai-agent/:sessionId')
  async toggleAiOrAgent(@Param() param: ToggleResponseDto) {
    return this.chatSessionService.toggleAiOrAgent(param.sessionId);
  }

  @Post('assign-chat-to-team')
  async assignChatToTeam(@Body() body: AssignChatToTeamDto) {
    return this.chatSessionService.assignChatToTeam(
      body.teamId,
      body.chatSessionId,
    );
  }
}
