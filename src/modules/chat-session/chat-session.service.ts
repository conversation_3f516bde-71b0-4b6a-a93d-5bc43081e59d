import {
  BadRequestException,
  Inject,
  Injectable,
  Logger,
  forwardRef,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Not, Repository } from 'typeorm';
import { v4 as uuidv4 } from 'uuid';
import { JwtService } from '@nestjs/jwt';
import * as moment from 'moment';

import { ChatSession } from './chat-session.entity';
import { ChatResponseGeneratorService } from '../chat-response-generator/chat-response-generator.service';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { Interaction } from '../../utils/enums/events/statistic';
import { BotCompany } from '../bot-company/bot-company.entity';
import { messageRole } from '../chat-response-generator/chat-response-generator.enum';
import { StatisticUpdateInteraction } from '../statistic/statistic.interface';
import { OpenAiService } from '../open-ai/open-ai.service';
import { ChatResponseGenerator } from '../chat-response-generator/chat-response-generator.entity';
import { ChatProvider } from '../../utils/enums/chatProvider';
import { Teams } from '../team-management/team-management.entity';
import { EmployeeService } from '../employee/employee.service';
import { WhatsappService } from '../whatsapp/whatsapp.service';
import { MessageTypes } from 'whatsapp-web.js';
import { ContactService } from '../contact/contact.service';
import { FacebookService } from '../facebook/facebook.service';

@Injectable()
export class ChatSessionService {
  private messages: Map<string, { role: messageRole; content: string }[]> =
    new Map();
  private timeoutId: NodeJS.Timeout;

  constructor(
    @InjectRepository(ChatSession)
    private chatSessionRepository: Repository<ChatSession>,
    @InjectRepository(BotCompany)
    private botCompanyRepository: Repository<BotCompany>,
    @InjectRepository(Teams)
    private teamsRepository: Repository<Teams>,
    @Inject(forwardRef(() => ChatResponseGeneratorService))
    private chatResponseGeneratorService: ChatResponseGeneratorService,
    @Inject(forwardRef(() => OpenAiService))
    private openAiService: OpenAiService,
    @Inject(forwardRef(() => EmployeeService))
    private employeeService: EmployeeService,
    private eventEmitter: EventEmitter2,
    private jwtService: JwtService,
    @Inject(forwardRef(() => WhatsappService))
    private whatsappService: WhatsappService,
    @Inject(forwardRef(() => ContactService))
    private contactService: ContactService,
    @Inject(forwardRef(() => FacebookService))
    private facebookService: FacebookService,
  ) {}

  async initChatSession(
    botRequestId: string,
    isEnglish: boolean,
    sessionId?: string,
  ) {
    const startedAt = moment();

    const chatExists = await this.chatSessionRepository.findOne({
      where: { clientId: sessionId },
      relations: ['chatResponseGenerator'],
    });

    const botCompany = await this.botCompanyRepository.findOne({
      where: { botRequestId },
      relations: ['company'],
    });

    if (chatExists && sessionId && chatExists?.clientId === sessionId) {
      const expireDate = new Date();
      expireDate.setHours(expireDate.getHours() + 24);
      // TO-DO: update expiration date on chatSession
      await this.chatSessionRepository.save(chatExists);
      const chatSessionUpdated = await this.chatSessionRepository.findOne({
        where: { clientId: sessionId },
        relations: ['chatResponseGenerator', 'botCompany'],
      });

      return {
        chat: chatSessionUpdated,
        history: chatSessionUpdated.chatResponseGenerator || [],
        sessionId,
      };
    } else {
      const newSessionId = uuidv4();
      const expireDate = new Date();

      expireDate.setHours(expireDate.getHours() + 24);

      let defaultTeam: Teams;
      const isDefaultTeamCreated = await this.teamsRepository.findOne({
        where: {
          company: { id: botCompany.company.id },
          isDefault: true,
        },
      });
      const isSalesTeamCreated = await this.teamsRepository.findOne({
        where: {
          company: { id: botCompany.company.id },
          isSales: true,
        },
      });

      if (!isDefaultTeamCreated) {
        defaultTeam = await this.teamsRepository.save({
          name: 'contact team',
          company: botCompany,
          isDefault: true,
        });
      }

      if (!isSalesTeamCreated) {
        await this.teamsRepository.save({
          name: 'sales team',
          company: botCompany,
          isSales: true,
        });
      }

      const chatSessionCreated = await this.chatSessionRepository.save({
        clientId: newSessionId,
        botCompany: botCompany,
        team: isDefaultTeamCreated ? isDefaultTeamCreated : defaultTeam,
        viewed: true,
        shouldAiResponse: botCompany?.isBotEnabled ? true : false,
      });

      this.eventEmitter.emit('notification.message.new', {
        chatSession: chatSessionCreated,
        team: isDefaultTeamCreated ? isDefaultTeamCreated : defaultTeam,
      });

      if (botCompany.chatProvider === ChatProvider.WHATSAPP) {
        return {
          chat: chatSessionCreated,
          history: [],
          sessionId: newSessionId,
        };
      }

      let welcomeMessage = botCompany.botInitialMessage;

      if (welcomeMessage && isEnglish) {
        const welcomeMessageTranslated = await this.openAiService.getResponse([
          {
            role: messageRole.SYSTEM,
            content:
              'Eres un experto traductor del idioma en-US, debes de traducir el texto que el usuario te va a suministrar.',
          },
          {
            role: messageRole.USER,
            content: welcomeMessage,
          },
        ]);
        welcomeMessage = welcomeMessageTranslated.choices[0]?.message?.content;
      }

      await this.chatResponseGeneratorService.saveInitialMessage(
        botCompany,
        chatSessionCreated,
      );

      if (welcomeMessage) {
        const chatResponseGeneratorCreated =
          await this.chatResponseGeneratorService.saveMessage(
            welcomeMessage,
            messageRole.ASSISTANT,
            chatSessionCreated,
            MessageTypes.TEXT,
          );

        return {
          chat: chatSessionCreated,
          history: [chatResponseGeneratorCreated],
          sessionId: newSessionId,
        };
      }

      return {
        chat: chatSessionCreated,
        history: [],
        sessionId: newSessionId,
      };
    }
  }

  async handleMessage(
    message: string,
    botCompany: BotCompany,
    sessionId: string,
    role: messageRole,
    whatsappNumer?: string,
    type?: MessageTypes,
  ) {
    const startedAt = moment();
    let response;

    const currentChat = await this.chatSessionRepository.findOne({
      where: { clientId: sessionId },
      relations: [
        'messageEntryPoint',
        'chatResponseGenerator',
        'whatsappChat',
        'botCompany',
        'botCompany.company',
        'team',
        'facebookChat',
      ],
      order: { chatResponseGenerator: { createdAt: 'ASC' } },
    });

    if (!currentChat) {
      throw new BadRequestException('Session id is not valid');
    }

    const contactExist =
      await this.contactService.getContactInformationByChatSessionId(
        currentChat.clientId,
      );
    let shouldAiResponseFromContact = true;

    if (contactExist) {
      shouldAiResponseFromContact = contactExist.shouldAiResponse;
    }

    if (
      !currentChat.botCompany.isBotEnabled ||
      !shouldAiResponseFromContact ||
      !currentChat.shouldAiResponse
    ) {
      if (role === messageRole.AGENT) {
        if (currentChat?.whatsappChat) {
          await this.whatsappService.responseMessage(
            currentChat.botCompany.botRequestId,
            currentChat.clientId,
            message,
          );
        }

        if (currentChat?.facebookChat) {
          await this.facebookService.responseToInstagram({
            message,
            recipientId: currentChat.facebookChat.senderId,
            token: '', // TODO: get token from social auth
          });
        }
      }

      await this.chatResponseGeneratorService.saveMessage(
        message,
        role,
        currentChat,
        type,
      );

      const chatSessionUpdated = await this.chatSessionRepository.findOne({
        where: {
          id: currentChat.id,
          chatResponseGenerator: {
            role: Not(messageRole.SYSTEM),
            content: Not(''),
          },
        },
        relations: [
          'chatResponseGenerator',
          'botCompany',
          'botCompany.company',
        ],
      });
      this.eventEmitter.emit('notification.message.new', {
        chatSession: chatSessionUpdated,
        team: currentChat.team,
      });
      return;
    }

    clearTimeout(this.timeoutId);

    const messagesBucket = this.messages.get(currentChat.clientId);
    if (!messagesBucket) {
      this.messages.set(currentChat.clientId, [
        { role: messageRole.USER, content: message },
      ]);
    } else {
      this.messages.set(currentChat.clientId, [
        ...messagesBucket,
        {
          role: messageRole.USER,
          content: message,
        },
      ]);
    }

    try {
      await new Promise((resolve) => {
        this.timeoutId = setTimeout(async () => {
          const allMessages = this.messages.get(currentChat.clientId);

          this.messages.delete(currentChat.clientId);

          response =
            await this.chatResponseGeneratorService.getResponseFromOpenIa(
              allMessages,
              currentChat.chatResponseGenerator,
              botCompany,
              currentChat,
              whatsappNumer,
            );

          const endDate = moment();
          const diff = endDate.diff(startedAt, 'millisecond');
          const millisecondInSecond = diff / 1000;

          const eventEmitterArgs: StatisticUpdateInteraction = {
            chatResponseGeneratedTimeInSecond: millisecondInSecond,
            chatSessionId: currentChat.id,
            role: messageRole.SYSTEM,
          };

          this.eventEmitter.emit(Interaction.Update, eventEmitterArgs);

          const chatSessionUpdated = await this.getChatSessionBySessionId(
            currentChat.clientId,
          );

          this.eventEmitter.emit('notification.message.new', {
            chatSession: chatSessionUpdated,
            team: currentChat.team,
          });
          resolve(null);
        }, 5000);
      });

      return response;
    } catch (error) {
      Logger.error(error);
    }
  }

  async getChatHistoryByBotRequestId(
    botRequestId: string,
  ): Promise<ChatResponseGenerator[]> {
    const currentChat = await this.chatSessionRepository.findOne({
      where: {
        chatResponseGenerator: {
          role: Not(messageRole.SYSTEM),
          content: Not(''),
        },
        botCompany: { botRequestId },
      },
      relations: ['chatResponseGenerator', 'messageEntryPoint', 'botCompany'],
    });

    return currentChat?.chatResponseGenerator || [];
  }

  async getChatSessionByEmployeeId(employeeId: number) {
    const employee = await this.employeeService.getOneEmployee(employeeId);

    if (!employee) {
      throw new BadRequestException('Invalid employee id');
    }

    const chatSessions = await this.chatSessionRepository.find({
      where: {
        chatResponseGenerator: {
          role: Not(messageRole.SYSTEM),
          content: Not(''),
        },
        botCompany: { company: { employees: { id: employeeId } } },
      },
      relations: [
        'chatResponseGenerator',
        'messageEntryPoint',
        'botCompany',
        'whatsappChat',
        'botCompany.company',
        'facebookChat',
      ],
      order: { createdAt: 'DESC' },
    });
    return chatSessions;
  }

  async getChatSessionBySessionId(sessionId: string): Promise<ChatSession> {
    return this.chatSessionRepository.findOne({
      where: {
        clientId: sessionId,
        chatResponseGenerator: {
          role: Not(messageRole.SYSTEM),
          content: Not(''),
        },
      },
      relations: [
        'chatResponseGenerator',
        'messageEntryPoint',
        'botCompany',
        'botCompany.company',
      ],
      order: { chatResponseGenerator: { createdAt: 'ASC' } },
    });
  }

  async getChatHistoryByChatSessionId(sessionId: string) {
    return this.chatSessionRepository.findOne({
      where: {
        clientId: sessionId,
        chatResponseGenerator: {
          role: Not(messageRole.SYSTEM),
          content: Not(''),
        },
      },
      relations: ['chatResponseGenerator'],
    });
  }

  async deleteChatSessionBySessionId(sessionId: string) {
    await this.chatSessionRepository.delete({ clientId: sessionId });
  }

  async toggleAiOrAgent(sessionId: string) {
    const chatSession = await this.getChatSessionBySessionId(sessionId);

    if (!chatSession) {
      throw new BadRequestException('Invalid session id');
    }

    chatSession.shouldAiResponse = !chatSession.shouldAiResponse;

    const contactExist =
      await this.contactService.getContactInformationByChatSessionId(
        chatSession.clientId,
      );

    if (contactExist) {
      await this.contactService.toggleAi(
        chatSession.botCompany.company.id,
        contactExist.id,
        false,
      );
    }

    return this.chatSessionRepository.save(chatSession);
  }

  async assignChatToTeam(teamId: number, chatSessionId: number) {
    const team = await this.teamsRepository.findOne({
      where: { id: teamId },
    });

    if (!team) {
      throw new BadRequestException('Invalid team id');
    }

    const chatSession = await this.chatSessionRepository.findOne({
      where: { id: chatSessionId },
    });

    if (!chatSession) {
      throw new BadRequestException('Invalid session id');
    }

    chatSession.team = team;

    return this.chatSessionRepository.save(chatSession);
  }

  async togglePotentialClientValue(
    chatSessionId: number,
    isPotential: boolean,
  ) {
    const chatSession = await this.chatSessionRepository.findOne({
      where: { id: chatSessionId },
    });
    chatSession.potentialClient = isPotential;

    return this.chatSessionRepository.save(chatSession);
  }

  async addTagsToChatSessionByChatSessionId(
    chatSessionId: number,
    tags: string[],
  ) {
    const chatSession = await this.chatSessionRepository.findOne({
      where: { id: chatSessionId },
    });

    if (!chatSession) {
      return;
    }

    chatSession.tags = tags;

    return this.chatSessionRepository.save(chatSession);
  }
}
