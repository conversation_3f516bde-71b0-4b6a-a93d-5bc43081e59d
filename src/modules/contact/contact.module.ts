import { Module, forwardRef } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

import { ContactService } from './contact.service';
import { Contact } from './contact.entity';
import { ContactController } from './contact.controller';
import { CompaniesModule } from '../companies/companies.module';
import { ChatSessionModule } from '../chat-session/chat-session.module';
import { ChatSession } from '../chat-session/chat-session.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([Contact, ChatSession]),
    forwardRef(() => CompaniesModule),
    forwardRef(() => ChatSessionModule),
  ],
  providers: [ContactService],
  exports: [ContactService],
  controllers: [ContactController],
})
export class ContactModule {}
