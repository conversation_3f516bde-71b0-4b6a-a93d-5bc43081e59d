import { IsBoolean, IsEmail, IsOptional, IsString } from 'class-validator';

export class CreateContactDto {
  @IsString()
  name: string;

  @IsEmail()
  @IsOptional()
  email: string;

  @IsString()
  @IsOptional()
  phone: string;

  @IsString()
  @IsOptional()
  chatSession: string;

  @IsBoolean()
  isWhatsAppNumber: boolean;

  @IsBoolean()
  shouldAiResponse: boolean;
}

export class ToggleAiDto {
  @IsBoolean()
  shouldAiResponse: boolean;
}

export class CreateContactFromChatSessionDto {
  @IsString()
  name: string;

  @IsEmail()
  @IsOptional()
  email: string;

  @IsString()
  @IsOptional()
  phone: string;

  @IsString()
  chatSession: string;
}
