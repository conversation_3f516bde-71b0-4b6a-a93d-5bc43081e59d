import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Put,
  Request,
  UseGuards,
} from '@nestjs/common';

import { ContactService } from './contact.service';
import { AuthGuard } from '../auth/auth.guard';
import {
  CreateContactDto,
  CreateContactFromChatSessionDto,
  ToggleAiDto,
} from './contact.dto';

@UseGuards(AuthGuard)
@Controller('contact')
export class ContactController {
  constructor(private contactService: ContactService) {}

  @Get()
  async getContactList(@Request() req) {
    return await this.contactService.getContactListByCompanyId(
      req.user.company.id,
    );
  }

  @Post()
  async createContact(@Request() req, @Body() body: CreateContactDto) {
    return await this.contactService.createContact(req.user.company.id, body);
  }

  @Delete(':id')
  async deleteContact(@Request() req, @Param('id') id: number) {
    return await this.contactService.deleteContact(
      req.user.company.id,
      Number(id),
    );
  }

  @Put(':id/toggle-ai')
  async updateContact(@Request() req, @Param('id') id: number) {
    return this.contactService.toggleAi(req.user.company.id, Number(id));
  }

  @Post('create-from-chat-session')
  async createContactFromChatSession(
    @Request() req,
    @Body() body: CreateContactFromChatSessionDto,
  ) {
    return await this.contactService.createContactFromChatSession(
      req.user.company.id,
      body,
    );
  }
}
