import {
  <PERSON>umn,
  CreateDate<PERSON><PERSON>umn,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  OneToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { ChatSession } from '../chat-session/chat-session.entity';

@Entity()
export class Contact {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ nullable: true })
  name: string;

  @Column({ nullable: true })
  phone: string;

  @Column({ nullable: true })
  email: string;

  @OneToOne(() => ChatSession, (chatSession) => chatSession.contact, {
    onDelete: 'SET NULL',
  })
  @JoinColumn()
  chatSession: ChatSession;

  @Column({ nullable: true })
  companyId: number;

  @Column({ nullable: false, default: false, type: 'boolean' })
  isWhatsAppNumber: boolean;

  @Column({ default: false, type: 'boolean' })
  shouldAiResponse: boolean;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
