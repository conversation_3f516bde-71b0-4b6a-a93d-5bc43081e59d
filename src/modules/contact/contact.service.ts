import {
  BadRequestException,
  ConflictException,
  Inject,
  Injectable,
  forwardRef,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Contact } from './contact.entity';
import { DataSource, Repository } from 'typeorm';
import { CompaniesService } from '../companies/companies.service';
import {
  CreateContactDto,
  CreateContactFromChatSessionDto,
} from './contact.dto';
import { ChatSessionService } from '../chat-session/chat-session.service';
import { ChatSession } from '../chat-session/chat-session.entity';

@Injectable()
export class ContactService {
  constructor(
    @InjectRepository(Contact) private contactRepository: Repository<Contact>,
    @InjectRepository(ChatSession)
    private chatSessionRepository: Repository<ChatSession>,
    private readonly companyService: CompaniesService,
    @Inject(forwardRef(() => ChatSessionService))
    private readonly chatSessionService: ChatSessionService,
  ) {}

  async getContactInformationByChatSessionId(
    chatSessionId: string,
  ): Promise<Contact> {
    return await this.contactRepository.findOne({
      where: { chatSession: { clientId: chatSessionId } },
      relations: ['chatSession'],
    });
  }

  async createContactFromChat(contactInformation: Contact) {
    let contact = await this.contactRepository.findOne({
      where: {
        chatSession: { clientId: contactInformation.chatSession.clientId },
      },
    });
    const company = await this.companyService.getCompanyByBotRequestId(
      contactInformation.chatSession.botCompany.botRequestId,
    );

    if (!contact) {
      contact = new Contact();
      if (contactInformation.chatSession)
        contact.chatSession = contactInformation.chatSession;
    }

    if (contactInformation.name) contact.name = contactInformation.name;
    if (contactInformation.phone) contact.phone = contactInformation.phone;
    if (contactInformation.email) contact.email = contactInformation.email;
    if (contactInformation.shouldAiResponse)
      contact.shouldAiResponse = contactInformation.shouldAiResponse;

    contact.companyId = company.id;

    return this.contactRepository.save(contact);
  }

  async getContactListByCompanyId(companyId: number) {
    return this.contactRepository.find({
      where: { companyId },
    });
  }

  async createContact(companyId: number, contactData: CreateContactDto) {
    // TODO: refactor the create contact code
    const contactExist = await this.contactRepository
      .createQueryBuilder('contact')
      .where('contact.companyId = :companyId', { companyId })
      .andWhere('(contact.phone = :phone OR contact.email = :email)', {
        phone: contactData?.phone,
        email: contactData?.email,
      })
      .getOne();

    if (contactExist && contactExist.companyId === companyId) {
      contactExist.phone = contactData.phone || contactExist.phone;
      contactExist.email = contactData.email || contactExist.email;
      contactExist.isWhatsAppNumber =
        contactData.isWhatsAppNumber || contactExist.isWhatsAppNumber;

      return this.contactRepository.save(contactExist);
    }

    const contact = new Contact();
    contact.name = contactData.name || '';
    contact.phone = contactData.phone || '';
    contact.email = contactData.email || '';
    contact.companyId = companyId;
    return this.contactRepository.save(contact);
  }

  async contactExistByCompanyId(companyId: number) {
    const contact = await this.contactRepository.findOne({
      where: { companyId },
    });

    if (!contact) {
      return null;
    }

    return contact;
  }

  async deleteContact(companyId: number, contactId: number) {
    return this.contactRepository.delete({ companyId, id: contactId });
  }

  async toggleAi(
    companyId: number,
    contactId: number,
    shouldAiResponse?: boolean,
  ) {
    const contact = await this.contactRepository.findOne({
      where: { companyId, id: contactId },
      relations: ['chatSession'],
    });
    contact.shouldAiResponse = shouldAiResponse || !contact.shouldAiResponse;

    if (contact.chatSession) {
      contact.chatSession.shouldAiResponse = contact.shouldAiResponse;
      await this.chatSessionRepository.save(contact.chatSession);
    }

    return this.contactRepository.save(contact);
  }

  async createContactFromChatSession(
    companyId: number,
    contactData: CreateContactFromChatSessionDto,
  ) {
    const isContactExist = await this.getContactInformationByChatSessionId(
      contactData.chatSession,
    );

    const chatSession = await this.chatSessionService.getChatSessionBySessionId(
      contactData.chatSession,
    );

    if (!chatSession) {
      throw new BadRequestException('Invalid session id');
    }

    if (isContactExist) {
      isContactExist.name = contactData.name || isContactExist.name;
      isContactExist.phone = contactData.phone || isContactExist.phone;
      isContactExist.email = contactData.email || isContactExist.email;
      isContactExist.chatSession = chatSession;

      return this.contactRepository.save(isContactExist);
    }

    const contactExistByPhoneOrEmail = await this.contactRepository
      .createQueryBuilder('contact')
      .where('contact.companyId = :companyId', { companyId })
      .andWhere('(contact.phone = :phone OR contact.email = :email)', {
        phone: contactData.phone,
        email: contactData.email,
      })
      .getOne();

    if (contactExistByPhoneOrEmail) {
      contactExistByPhoneOrEmail.name =
        contactData.name || contactExistByPhoneOrEmail.name;
      contactExistByPhoneOrEmail.phone =
        contactData.phone || contactExistByPhoneOrEmail.phone;
      contactExistByPhoneOrEmail.email =
        contactData.email || contactExistByPhoneOrEmail.email;
      contactExistByPhoneOrEmail.chatSession = chatSession;

      return this.contactRepository.save(contactExistByPhoneOrEmail);
    }

    const contact = new Contact();
    contact.name = contactData.name || '';
    contact.phone = contactData.phone || '';
    contact.email = contactData.email || '';
    contact.companyId = companyId;
    contact.chatSession = chatSession;

    return this.contactRepository.save(contact);
  }
}
