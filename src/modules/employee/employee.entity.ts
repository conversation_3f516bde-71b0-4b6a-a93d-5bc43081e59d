import {
  <PERSON>tity,
  Column,
  PrimaryGeneratedColumn,
  ManyToOne,
  BeforeInsert,
  BeforeUpdate,
  OneToOne,
  JoinColumn,
  UpdateDateColumn,
  CreateDateColumn,
  ManyToMany,
  JoinTable,
  OneToMany,
} from 'typeorm';
import * as bcrypt from 'bcryptjs';

import { Company } from '../companies/companies.entity';
import { Auth } from '../auth/auth.entity';
import { Teams } from '../team-management/team-management.entity';

@Entity()
export class Employee {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ nullable: true, default: '' })
  name: string;

  @Column({ nullable: true, default: '' })
  email: string;

  @Column({ nullable: false })
  password: string;

  @ManyToOne(() => Company, (company) => company.employees)
  company: Company;

  @Column({ type: 'boolean', nullable: false, default: false })
  isDeleted: boolean;

  @Column({ nullable: true, type: 'boolean', default: true })
  canShow: boolean;

  @Column({ nullable: false, default: false })
  shouldUpdatePassword: boolean;

  @ManyToOne(() => Teams, (team) => team.employees)
  team: Teams;

  @OneToOne(() => Auth, (auth) => auth.employee)
  @JoinColumn()
  auth: Auth;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @BeforeInsert()
  @BeforeUpdate()
  async hashPassword() {
    if (this.password) {
      this.password = await bcrypt.hash(this.password, 10);
    }
  }

  async comparePassword(attempt: string): Promise<boolean> {
    return await bcrypt.compare(attempt, this.password);
  }
}
