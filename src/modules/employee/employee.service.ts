import { BadRequestException, Injectable } from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { InjectRepository } from '@nestjs/typeorm';
import { TypeOrmCrudService } from '@nestjsx/crud-typeorm';
import { Repository } from 'typeorm';

import { Auth } from '../auth/auth.entity';
import { Company } from '../companies/companies.entity';
import { CreateEmployeeDto, UpdateEmployeeDto } from './employee.dto';
import { Employee } from './employee.entity';

@Injectable()
export class EmployeeService extends TypeOrmCrudService<Employee> {
  constructor(
    @InjectRepository(Employee) repo: Repository<Employee>,
    @InjectRepository(Company) private companyRepo: Repository<Company>,
    @InjectRepository(Auth) private authRepo: Repository<Auth>,
    private eventEmitter: EventEmitter2,
  ) {
    super(repo);
  }

  async getEmployeesByCompanyId(companyId: number) {
    const employees = await this.repo.find({
      where: { company: { id: companyId }, isDeleted: false },
      relations: ['auth'],
    });

    return employees;
  }

  async getOneEmployee(employeeId: number) {
    return this.repo.findOne({
      where: { id: employeeId },
      relations: ['company'],
    });
  }

  async deleteEmployee(employeeId: number) {
    const employee = await this.repo.findOne({
      where: { id: employeeId },
      relations: ['company', 'company.employees', 'auth'],
    });

    const employees = employee.company.employees.filter(
      (employee) => employee.isDeleted === false,
    );

    if (employees.length === 1 && employees[0].id == employeeId) {
      throw new BadRequestException('cannot delete the only admin');
    }

    employee.isDeleted = true;
    employee.canShow = false;
    employee.auth.isEmailConfirmed = false;
    await this.repo.save(employee);
    await this.authRepo.save(employee.auth);

    return this.getEmployeesByCompanyId(employee.company.id);
  }

  async createEmployeeFromAdmin(
    employeeId: number,
    employee: CreateEmployeeDto,
  ) {
    const employeeExist = await this.repo.findOne({
      where: { email: employee?.email },
    });

    if (employeeExist) {
      if (employeeExist.isDeleted) {
        employeeExist.isDeleted = false;
        employeeExist.password = employee?.password;
        employeeExist.name = employee?.name;
        employeeExist.canShow = true;

        await this.repo.save(employeeExist);

        this.eventEmitter.emit(
          'auth.createEmailValidationToken',
          employee?.email,
        );
        return employeeExist;
      }

      throw new BadRequestException('user already exist');
    }

    const auth = new Auth();

    await this.authRepo.save(auth);

    const currentEmployee = await this.repo.findOne({
      where: { id: employeeId },
      relations: ['company'],
    });

    const employeeToCreate = new Employee();
    employeeToCreate.name = employee?.name;
    employeeToCreate.email = employee?.email;
    employeeToCreate.password = employee?.password;
    employeeToCreate.company = currentEmployee.company;
    employeeToCreate.auth = auth;

    const employeeCreated = await this.repo.save(employeeToCreate);

    delete employeeCreated.password;

    this.eventEmitter.emit('auth.createEmailValidationToken', employee?.email);

    return employeeCreated;
  }

  async createEmployee(employee: CreateEmployeeDto) {
    const employeeExist = await this.repo.findOne({
      where: { email: employee?.email },
    });

    if (employeeExist) {
      throw new BadRequestException('user already exist');
    }

    const auth = new Auth();

    await this.authRepo.save(auth);

    const company = await this.companyRepo.findOne({
      where: { id: employee.companyId },
    });

    const employeeToCreate = new Employee();
    employeeToCreate.name = employee?.name;
    employeeToCreate.email = employee?.email;
    employeeToCreate.password = employee?.password;
    employeeToCreate.company = company;
    employeeToCreate.shouldUpdatePassword = true;
    employeeToCreate.auth = auth;

    const employeeCreated = await this.repo.save(employeeToCreate);
    auth.employee = employeeCreated;
    auth.isEmailConfirmed = false;
    await this.authRepo.save(auth);

    delete employeeCreated.password;

    return employeeCreated;
  }

  async getEmployeeByEmail(email: string) {
    return this.repo.findOne({
      where: { email },
      relations: ['auth', 'company'],
    });
  }

  async getEmployeeByEmailAndPassword(email: string, password: string) {
    const employee = await this.getEmployeeByEmail(email);

    if (!employee) {
      throw new BadRequestException('invalid credentials');
    }

    const isPasswordValid = await employee.comparePassword(password);

    if (!isPasswordValid) {
      throw new BadRequestException('invalid credentials');
    }

    return employee;
  }

  async getEmployeeByAuthId(authId: number) {
    return this.repo.findOne({
      where: { auth: { id: authId } },
      relations: ['auth', 'company'],
    });
  }

  async updateEmployeeInfo(employeeId: number, employee: UpdateEmployeeDto) {
    const employeeToUpdate = await this.getOneEmployee(employeeId);
    delete employeeToUpdate.password;

    if (!employeeToUpdate) {
      throw new BadRequestException('employee don`t exist');
    }

    employeeToUpdate.name = employee?.name || employeeToUpdate.name;
    employeeToUpdate.email = employee?.email || employeeToUpdate.email;

    if (employee?.password) {
      employeeToUpdate.password = employee.password;
      employeeToUpdate.shouldUpdatePassword = false;
    }

    return this.repo.save(employeeToUpdate);
  }

  async updateEmployeePassword(authId: number, newPassword: string) {
    const employee = await this.getEmployeeByAuthId(authId);

    employee.password = newPassword;
    employee.shouldUpdatePassword = false;

    return this.repo.save(employee);
  }
}
