import {
  Body,
  Controller,
  Delete,
  Get,
  HttpCode,
  Param,
  Post,
  Put,
  Req,
  UseGuards,
} from '@nestjs/common';
import { AuthGuard } from '../auth/auth.guard';
import { CreateEmployeeDto, UpdateEmployeeDto } from './employee.dto';

import { Employee } from './employee.entity';
import { EmployeeService } from './employee.service';

@UseGuards(AuthGuard)
@Controller('employee')
export class EmployeeController {
  constructor(private service: EmployeeService) {}

  @Post()
  async createEmployee(@Body() body: CreateEmployeeDto, @Req() req: any) {
    return this.service.createEmployeeFromAdmin(req.user.sub, body);
  }

  @Get()
  async getEmployees(@Req() req: any) {
    return this.service.getEmployeesByCompanyId(req.user.company.id);
  }

  @HttpCode(200)
  @Delete(':id')
  async deleteEmployee(@Param() param: any) {
    return this.service.deleteEmployee(param.id);
  }

  @Put(':id')
  updateEmployeeInfo(@Param() param: any, @Body() body: UpdateEmployeeDto) {
    return this.service.updateEmployeeInfo(param.id, body);
  }
}
