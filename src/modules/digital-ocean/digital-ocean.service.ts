import { Injectable } from '@nestjs/common';
import {
  DeleteObjectCommand,
  GetObjectCommand,
  ListObjectsCommand,
  PutObjectCommand,
  S3,
} from '@aws-sdk/client-s3';
import { getSignedUrl } from '@aws-sdk/s3-request-presigner';
import {
  writeFileSync,
  existsSync,
  mkdirSync,
  readFileSync,
  readdirSync,
  statSync,
} from 'fs';
import { Upload } from '@aws-sdk/lib-storage';
import path = require('path');
import { Readable } from 'stream';

@Injectable()
export class DigitalOceanService {
  private s3Client: S3;

  constructor() {
    this.s3Client = new S3({
      endpoint: process.env.SPACES_URL,
      region: process.env.SPACES_REGION,
      credentials: {
        accessKeyId: process.env.SPACES_KEY,
        secretAccessKey: process.env.SPACES_SECRET,
      },
    });
  }

  async deleteFileFromBucket(bucketName: string, fileName: string) {
    const params = {
      Bucket: bucketName,
      Key: fileName,
    };

    const command = new DeleteObjectCommand(params);
    const data = await this.s3Client.send(command);
    return data;
  }

  async getFileFromBucket(bucketName: string, fileName: string) {
    const params = {
      Bucket: bucketName,
      Key: fileName,
    };

    const command = new GetObjectCommand(params);
    const data = await this.s3Client.send(command);
    return data;
  }

  async getAllFilesFromBucket(
    bucketName: 'whatsapp-auth' | 'biitbot-public-assets' | string,
    prefix?: string,
  ) {
    const data = await this.s3Client.send(
      new ListObjectsCommand({ Bucket: bucketName, Prefix: prefix }),
    );
    return data;
  }

  async streamToString(stream): Promise<string> {
    const chunks = [];
    return new Promise((resolve, reject) => {
      stream.on('data', (chunk) => {
        return chunks.push(Buffer.from(chunk));
      });
      stream.on('error', (err) => {
        console.log('error');
        reject(err);
      });
      stream.on('end', () => {
        resolve(Buffer.concat(chunks).toString('utf8'));
      });
    });
  }

  async downloadFileFromBucket(
    bucketName: 'whatsapp-auth' | 'biitbot-public-assets' | string,
    fileName: string,
  ) {
    const destinationFolder = `${process.env.INIT_CWD}/.whatsapp-auth`;

    const files = await this.getAllFilesFromBucket(bucketName, fileName);

    if (!existsSync(destinationFolder)) {
      mkdirSync(destinationFolder, { recursive: true });
    }

    if (!files?.Contents) {
      return;
    }

    for (const file of files?.Contents) {
      const currentFile = await this.getFileFromBucket(bucketName, file.Key);
      const data = await this.streamToString(currentFile.Body);
      const filePath = path.join(destinationFolder, file.Key);
      const fileDirname = path.dirname(filePath);

      if (!existsSync(fileDirname)) {
        mkdirSync(fileDirname, { recursive: true });
      }

      writeFileSync(filePath, data);
    }
  }

  async uploadFileToBucket(
    bucketName: 'biitbot-public-assets' | 'whatsapp-auth',
    path: string,
    file: Express.Multer.File | any,
    metadata?: { isFile?: boolean; isFolder?: boolean },
  ) {
    const params: any = {
      Bucket: bucketName,
      Key: path.replace(/\\/g, '/'),
      Body: file.buffer,
      ACL: 'public-read',
      ContentType: file.mimetype,
      Metadata: metadata,
    };

    const command = new PutObjectCommand(params);
    const data = await this.s3Client.send(command).catch((err) => {
      console.log('error uploading file to bucket', err);
    });
    return data;
  }

  async uploadMultipleFilesToBucket(
    originPath: string,
    bucketName: 'biitbot-public-assets' | 'whatsapp-auth',
    remoteFolderName: string,
  ) {
    try {
      const files = readdirSync(path.join(originPath));

      for (const file of files) {
        const fullPatch = path.join(originPath, file);
        const stats = statSync(fullPatch);

        if (stats.isFile()) {
          try {
            const fileData = readFileSync(fullPatch);
            const fileStream = new Readable();
            fileStream.push(fileData);
            fileStream.push(null);

            this.uploadFileToBucket(
              bucketName,
              path.join(remoteFolderName, file),
              fileStream,
            );
          } catch (error) {
            console.error('Error al subir archivo:', error);
          }
        } else if (stats.isDirectory()) {
          await this.uploadMultipleFilesToBucket(
            fullPatch,
            bucketName,
            path.join(remoteFolderName, file),
          );
        }
      }
    } catch (error) {
      console.error('Error al listar y subir archivos:', error);
    }
  }

  async uploadZipFileToBucket(
    bucketName: 'biitbot-public-assets' | 'whatsapp-auth',
    path: string,
    filePath: string,
  ) {
    const file = readFileSync(filePath);

    const parallelUploads3 = new Upload({
      client: this.s3Client,
      params: {
        Bucket: bucketName,
        Key: path,
        Body: file,
        ContentType: 'application/zip',
      },
    });

    try {
      parallelUploads3.on('httpUploadProgress', (progress) => {
        console.log(progress);
      });

      await parallelUploads3.done();
    } catch (error) {
      console.log('error uploading file to bucket', error);
    }
  }

  async getSignedUrl(
    bucketName: 'biitbot-public-assets' | 'whatsapp-auth',
    fileName: string,
  ) {
    const bucketParams = {
      Bucket: bucketName,
      Key: fileName,
    };

    try {
      const url = await getSignedUrl(
        this.s3Client,
        new GetObjectCommand(bucketParams),
        { expiresIn: 3600 },
      );
      return url;
    } catch (err) {
      console.log('Error', err);
    }
  }
}
