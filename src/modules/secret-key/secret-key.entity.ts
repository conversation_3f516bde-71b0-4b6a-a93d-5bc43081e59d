import {
  Column,
  Entity,
  ManyToOne,
  PrimaryGeneratedColumn,
  CreateDateColumn,
  UpdateDateColumn,
} from 'typeorm';
import { BotCompany } from '../bot-company/bot-company.entity';

@Entity()
export class SecretKey {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ nullable: false, type: 'bytea' })
  secretKey: Buffer;

  @ManyToOne(() => BotCompany, (botCompany) => botCompany.secretKeys)
  company: BotCompany;

  @CreateDateColumn()
  created_at: Date;

  @UpdateDateColumn()
  updated_at: Date;
}
