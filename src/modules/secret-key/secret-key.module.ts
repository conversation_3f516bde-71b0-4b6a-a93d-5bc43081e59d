import { <PERSON>du<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

import { BotCompany } from '../bot-company/bot-company.entity';
import { <PERSON><PERSON><PERSON> } from './secret-key.entity';
import { SecretKeyService } from './secret-key.service';

@Module({
  imports: [TypeOrmModule.forFeature([SecretKey, BotCompany])],
  providers: [SecretKeyService],
  exports: [SecretKeyService],
})
export class SecretKeyModule {}
