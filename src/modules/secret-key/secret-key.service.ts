import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { randomBytes } from 'crypto';

import { SecretKey } from './secret-key.entity';
import { BotCompany } from '../bot-company/bot-company.entity';

@Injectable()
export class SecretKeyService {
  constructor(
    @InjectRepository(SecretKey)
    private secretKeyRepository: Repository<SecretKey>,
    @InjectRepository(BotCompany)
    private botCompanyRepository: Repository<BotCompany>,
  ) {}

  async createSecretKey(companyId: number) {
    const company = await this.botCompanyRepository.findOne({
      where: { company: { id: companyId } },
    });

    if (!company) {
      throw new Error('Company not found');
    }
    const secretKey = randomBytes(32);

    const newSecretKey = new SecretKey();
    newSecretKey.company = company;
    newSecretKey.secretKey = secretKey;

    return this.secretKeyRepository.save(newSecretKey);
  }

  async createBotRequestId() {
    const id = randomBytes(5).toString('hex');
    return id;
  }
}
