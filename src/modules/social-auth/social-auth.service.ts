import {
  BadRequestException,
  forwardRef,
  Inject,
  Injectable,
  InternalServerErrorException,
  Logger,
  NotFoundException,
} from '@nestjs/common';
import { Repository } from 'typeorm';
import { SocialAuth } from './social-auth.entity';
import { InjectRepository } from '@nestjs/typeorm';
import { google } from 'googleapis';
import * as fs from 'fs';

import { BotCompanyService } from '../bot-company/bot-company.service';
import {
  SocialAuthStatus,
  SocialAuthType,
} from './social-auth.interface';
import { OAuth2Client } from 'google-auth-library';
import { CalendarsService } from '../calendars/calendars.service';
import { Cron, CronExpression } from '@nestjs/schedule';
import { ChatProvider } from '../../utils/enums/chatProvider';
import { CompleteInstagramIntegrationDto } from './social-auth.dto';
import { FacebookService } from '../facebook/facebook.service';
import axios from 'axios';
import { SocialAuthAccount } from './social-auth-account.entity';
import { Calendars } from '../calendars/calendars.entity';

@Injectable()
export class SocialAuthService {
  private readonly logger = new Logger(SocialAuthService.name);
  public googleClient: OAuth2Client;
  private readonly TOKEN_EXCHANGE_URL =
    'https://graph.facebook.com/v22.0/oauth/access_token'; // Use your desired API version
  private readonly ACCOUNTS_URL =
    'https://graph.facebook.com/v22.0/me/accounts'; // Use your desired API version
  private FACEBOOK_APP_ID = process.env.FACEBOOK_APP_ID; // or this.configService.get<string>('FACEBOOK_APP_ID');
  private FACEBOOK_APP_SECRET = process.env.FACEBOOK_APP_SECRET; // or this.configService.get<string>('FACEBOOK_APP_SECRET');

  constructor(
    @InjectRepository(SocialAuth)
    private socialAuthRepo: Repository<SocialAuth>,
    @InjectRepository(SocialAuthAccount)
    private socialAuthAccountRepo: Repository<SocialAuthAccount>,
    @InjectRepository(Calendars)
    private calendarsRepo: Repository<Calendars>,
    @Inject(forwardRef(() => CalendarsService))
    private calendarsService: CalendarsService,
    @Inject(forwardRef(() => FacebookService))
    private facebookConfigService: FacebookService,
    @Inject(forwardRef(() => BotCompanyService))
    private botCompanyService: BotCompanyService,
  ) {
    const configPath = fs.readFileSync(
      `${process.cwd()}/biitbot-google-auth.json`,
      'utf8',
    );
    const googleCredentials = JSON.parse(configPath).web;

    this.googleClient = new google.auth.OAuth2(
      googleCredentials.client_id,
      googleCredentials.client_secret,
      googleCredentials.redirect_uris[0],
    );
  }

  async getAllIntegration(companyId: number) {
    return this.socialAuthRepo.find({
      where: { company: { id: companyId } },
    });
  }

  async getIntegration(socialAuthId: string) {
    return this.socialAuthRepo.findOne({ where: { id: socialAuthId } });
  }

  getSocialAuthAccountByAccountId = async (
    accountId: string,
  ): Promise<SocialAuthAccount> => {
    const socialAuthAccount = await this.socialAuthAccountRepo.findOne({
      where: { accountId },
      relations: ['socialAuth'],
    });

    if (!socialAuthAccount) {
      throw new NotFoundException('Integration not exist');
    }

    return socialAuthAccount;
  };

  async unlinkIntegration(socialAuthId: string) {
    const socialAuthAccount = await this.socialAuthAccountRepo.findOne({
      where: { id: socialAuthId },
    });

    if (!socialAuthAccount) {
      throw new NotFoundException('Integration not exist');
    }

    socialAuthAccount.selectedBotCompany = null;
    socialAuthAccount.status = SocialAuthStatus.DISCONNECTED;

    await this.socialAuthAccountRepo.save(socialAuthAccount);
    return socialAuthAccount;
  }

  async deleteIntegration(accountId: string) {
    try {
      const socialAuthAccount = await this.socialAuthAccountRepo.findOne({
        where: { id: accountId },
      });

      if (!socialAuthAccount) {
        throw new NotFoundException('Integration not exist');
      }

      // TODO: delete calendar integration

      await this.socialAuthAccountRepo.remove(socialAuthAccount);
    } catch (error) {
      Logger.error('error removing integration', error);
      throw new BadRequestException('error removing integration');
    }
  }

  async changeSocialAuthSelected(
    socialAuthId: string,
    botRequestId: string,
    companyId: number,
  ) {
    const socialAuthAccount = await this.socialAuthAccountRepo.findOne({
      where: { id: socialAuthId },
    });

    if (!socialAuthAccount) {
      throw new NotFoundException(
        'Social authentication record not found or does not belong to your company.',
      );
    }

    const targetBotCompany =
      await this.botCompanyService.getBotCompanyByBotRequestId(botRequestId);

    // Verify the botCompany belongs to the same company as the user and the socialAuth record
    if (!targetBotCompany || targetBotCompany.company.id !== companyId) {
      throw new NotFoundException(
        'Bot company not found or does not belong to your company.',
      );
    }

    socialAuthAccount.selectedBotCompany = targetBotCompany;
    await this.socialAuthAccountRepo.save(socialAuthAccount);
    return socialAuthAccount;
  }

  async getSocialIntegrationByCompanyId(companyId: number) {
    return this.socialAuthRepo.findOne({
      where: {
        company: { id: companyId },
      },
    });
  }

  async completeInstagramIntegration(
    chatProvider: ChatProvider,
    body: CompleteInstagramIntegrationDto,
  ) {
    const botCompany = await this.botCompanyService.getBotCompanyByBotRequestId(
      body?.botRequestId,
    );

    if (!botCompany) {
      new BadRequestException('bot not exist');
    }

    const socialAuthAccountExist = await this.socialAuthAccountRepo.findOne({
      where: {
        accountId: body.accountId,
      },
    });

    if (chatProvider === ChatProvider.INSTAGRAM) {
      const socialAuthExist = await this.socialAuthRepo.findOne({
        where: {
          company: { id: botCompany.company.id },
        },
      });

      const socialAuth = socialAuthExist ? socialAuthExist : new SocialAuth();
      socialAuth.company = botCompany.company;
      await this.socialAuthRepo.save(socialAuth);

      const socialAuthAccount = socialAuthAccountExist
        ? socialAuthAccountExist
        : new SocialAuthAccount();
      socialAuthAccount.accountId = body.accountId;
      socialAuthAccount.accountName = body.accountName;
      socialAuthAccount.type = SocialAuthType.CHAT;
      socialAuthAccount.provider = chatProvider;
      socialAuthAccount.status = SocialAuthStatus.CONNECTED;
      socialAuthAccount.token = body.token;
      socialAuthAccount.selectedBotCompany = botCompany;
      socialAuthAccount.socialAuth = socialAuth;
      await this.socialAuthAccountRepo.save(socialAuthAccount);

      return {
        status: 'connected',
      };
    }
  }

  async exchangeCodeForTokens(code: string, botRequestId: string) {
    try {
      // Prepare the parameters in application/x-www-form-urlencoded format
      const params = new URLSearchParams();
      params.append('client_id', this.FACEBOOK_APP_ID);
      params.append('client_secret', this.FACEBOOK_APP_SECRET);
      params.append('grant_type', 'authorization_code');
      params.append(
        'redirect_uri',
        'https://caiman-legal-treefrog.ngrok-free.app/social-auth/facebook/callback',
      ); // The exact redirect URI
      params.append('code', code); // The authorization code received

      const tokenResponse = await axios.post(this.TOKEN_EXCHANGE_URL, params);
      const { access_token } = tokenResponse.data;
      const accountsResponse = await axios.get(this.ACCOUNTS_URL, {
        params: {
          access_token,
        },
      });
      const { data } = accountsResponse.data;

      const instagramAccounts = await Promise.all(
        data.map(async (facebookAccount) => {
          try {
            const instagramAccountResponse = await axios.get(
              `https://graph.facebook.com/v22.0/${facebookAccount.id}/instagram_accounts?fields=name&access_token=${facebookAccount.access_token}`,
              {
                headers: {
                  'Content-Type': 'application/json',
                },
              },
            );

            return {
              ...instagramAccountResponse?.data?.data[0],
              access_token: facebookAccount.access_token,
            };
          } catch (error) {
            Logger.error('Error fetching Instagram accounts', error);
            return null;
          }
        }),
      );

      if (Array.isArray(data)) {
        await Promise.all(
          instagramAccounts.map((account) => {
            return this.completeInstagramIntegration(ChatProvider.INSTAGRAM, {
              token: account.access_token,
              botRequestId,
              accountId: account.id,
              accountName: account.name,
            });
          }),
        );
      }

      return {
        status: 'connected',
        botRequestId,
      };
    } catch (error) {
      throw error;
    }
  }

  async generateGoogleAuthUrl(botRequestId: string) {
    const url = this.googleClient.generateAuthUrl({
      access_type: 'offline',
      scope: [
        'https://www.googleapis.com/auth/userinfo.email',
        'https://www.googleapis.com/auth/calendar',
        'https://www.googleapis.com/auth/calendar.events',
      ],
      state: botRequestId,
      prompt: 'consent',
    });

    return url;
  }

  async handleGoogleCallback(botRequestId: string, code: string) {
    const { tokens } = await this.googleClient.getToken(code);
    const tokenInfo = await this.googleClient.getTokenInfo(tokens.access_token);

    this.logger.log(
      `Google token info obtained for email: ${tokenInfo.email}, user ID (sub): ${tokenInfo.sub}`,
    );

    // Check if a calendar is already integrated for this email
    this.logger.log(
      `Checking for existing calendar integration with email: ${tokenInfo.email}`,
    );
    const existingCalendar = await this.calendarsRepo.findOne({
      where: { email: tokenInfo.email },
      relations: ['socialAuthAccount', 'socialAuthAccount.socialAuth'],
    });

    if (existingCalendar) {
      this.logger.log(
        `Calendar already integrated for email ${tokenInfo.email} (Calendar ID: ${existingCalendar.id}). Returning associated SocialAuth.`,
      );
      if (
        existingCalendar.socialAuthAccount &&
        existingCalendar.socialAuthAccount.socialAuth
      ) {
        // Optionally, consider updating tokens on existingCalendar.socialAuthAccount here if needed.
        // For now, strictly adhering to "don't continue with the process".
        return existingCalendar.socialAuthAccount.socialAuth;
      } else {
        this.logger.error(
          `Inconsistent state: Calendar found for email ${tokenInfo.email} (ID: ${existingCalendar.id}), but missing socialAuthAccount or socialAuth relation. This should not happen. Manual review required.`,
        );
        throw new InternalServerErrorException(
          `Inconsistent calendar integration state for email: ${tokenInfo.email}. Please contact support.`,
        );
      }
    }

    this.logger.log(
      `No existing calendar found for email ${tokenInfo.email}. Proceeding with new/updated integration.`,
    );

    const botCompany =
      await this.botCompanyService.getBotCompanyByBotRequestId(botRequestId);

    if (!botCompany) {
      throw new NotFoundException('Bot not exist');
    }

    const socialAuthExist = await this.socialAuthRepo.findOne({
      where: {
        company: { id: botCompany.company.id },
      },
    });

    const socialAuth = socialAuthExist ? socialAuthExist : new SocialAuth();
    socialAuth.company = botCompany.company;
    await this.socialAuthRepo.save(socialAuth);

    this.logger.log(
      `Handling Google Callback: Looking for SocialAuthAccount with accountId (tokens.id_token): ${tokens.id_token}`,
    );
    const socialAuthAccountExist = await this.socialAuthAccountRepo.findOne({
      where: {
        accountId: tokenInfo.email,
      },
    });

    let socialAuthAccount: SocialAuthAccount;
    if (socialAuthAccountExist) {
      this.logger.log(
        `Found existing SocialAuthAccount with id: ${socialAuthAccountExist.id}. Updating it.`,
      );
      socialAuthAccount = socialAuthAccountExist;
    } else {
      this.logger.log(
        `No existing SocialAuthAccount found for accountId: ${tokenInfo.email}. Creating new one.`,
      );
      socialAuthAccount = new SocialAuthAccount();
    }

    socialAuthAccount.accountId = tokenInfo.email;
    socialAuthAccount.token = tokens.access_token;
    socialAuthAccount.refreshToken = tokens.refresh_token;
    socialAuthAccount.type = SocialAuthType.CALENDAR;
    socialAuthAccount.status = SocialAuthStatus.CONNECTED;
    socialAuthAccount.provider = ChatProvider.GOOGLE;
    socialAuthAccount.selectedBotCompany = botCompany;
    socialAuthAccount.socialAuth = socialAuth;
    socialAuthAccount.accountName = tokenInfo.email;
    await this.socialAuthAccountRepo.save(socialAuthAccount);

    const calendar = await this.calendarsService.handleGoogleCalendarIntegration(
      socialAuthAccount,
    );
    socialAuthAccount.calendar = calendar; // Link the returned calendar
    await this.socialAuthAccountRepo.save(socialAuthAccount); // Save the updated relation

    return socialAuth;
  }

  async getSocialAuthByBotRequestId(botRequestId: string) {
    return this.socialAuthRepo.findOne({
      where: {
        socialAuthAccount: {
          selectedBotCompany: { botRequestId },
        },
      },
      relations: ['socialAuthAccount.selectedBotCompany', 'company', 'socialAuthAccount.calendar'],
    });
  }

  async getSocialAuthByAccountId(accountId: string) {
    return this.socialAuthRepo.findOne({
      where: { socialAuthAccount: { accountId } },
    });
  }

  @Cron(CronExpression.EVERY_30_MINUTES)
  async refreshGoogleToken() {
    // const botRequestList = await this.socialAuthRepo.find({
    //   where: { source: AvailableIntegration.GOOGLE, validGrant: true },
    // });
    //
    // if (botRequestList.length > 0) {
    //   for (const botRequest of botRequestList) {
    //     try {
    //       const currentGoogleAuth = this.googleClient;
    //       currentGoogleAuth.setCredentials({
    //         access_token: botRequest.token,
    //         refresh_token: botRequest.refreshToken,
    //       });
    //       const tokens = await currentGoogleAuth.refreshAccessToken();
    //       Logger.debug(
    //         `refreshing token ${botRequest?.selectedBotCompany?.botRequestId}`,
    //         tokens,
    //       );
    //
    //       botRequest.token = tokens.credentials.access_token;
    //       botRequest.refreshToken = tokens.credentials.refresh_token;
    //
    //       await this.socialAuthRepo.save(botRequest);
    //     } catch (error) {
    //       botRequest.status = SocialAuthStatus.DISCONNECTED;
    //       botRequest.validGrant = false;
    //
    //       await this.socialAuthRepo.save(botRequest);
    //
    //       Logger.error('error refreshing google token', error);
    //     }
    //   }
    // }
  }
}
