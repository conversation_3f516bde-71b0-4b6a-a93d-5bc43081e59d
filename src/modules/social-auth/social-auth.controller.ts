import {
  Body,
  Controller,
  Delete,
  Get,
  Logger,
  Param,
  Post,
  Put,
  Query,
  Req,
  Response,
  UseGuards,
} from '@nestjs/common';
import { AuthGuard } from '../auth/auth.guard';
import { SocialAuthService } from './social-auth.service';
import {
  CompleteInstagramIntegrationDto,
  UpdateSocialAuthSelected,
} from './social-auth.dto';
import { ChatProvider } from '../../utils/enums/chatProvider';

@UseGuards(AuthGuard)
@Controller('social-auth')
export class SocialAuthController {
  constructor(private socialAuthService: SocialAuthService) {}

  @Get('/integration/:id')
  async getSocialAuthIntegrated(@Param() param) {
    return this.socialAuthService.getIntegration(param.id);
  }

  @Post(':chatProvider/continue')
  async handleCompleteSocialIntegration(
    @Param('chatProvider') chatProvider: ChatProvider,
    @Body() body: CompleteInstagramIntegrationDto,
  ) {
    return this.socialAuthService.completeInstagramIntegration(
      chatProvider,
      body,
    );
  }

  @Get()
  async getSocialIntegrationList(@Req() req) {
    return this.socialAuthService.getAllIntegration(req.user.company.id);
  }

  @Get('botCompany')
  async getSocialIntegrationByCompanyId(@Req() req) {
    return this.socialAuthService.getSocialIntegrationByCompanyId(
      req.user.company.id,
    );
  }

  @Put('integration/:integrationId/unlink')
  async unlinkSocialAuthIntegration(@Param() param) {
    return this.socialAuthService.unlinkIntegration(param.integrationId);
  }

  @Delete('integration/:accountId')
  async deleteSocialAuthIntegration(@Param() param) {
    return this.socialAuthService.deleteIntegration(param.accountId);
  }

  @Put('integration/select')
  async updateSocialAuthSelected(
    @Body() body: UpdateSocialAuthSelected,
    @Req() req,
  ) {
    return this.socialAuthService.changeSocialAuthSelected(
      body.socialAuthId,
      body.botRequestId,
      req.user.company.id,
    );
  }

  @Get('/google/:botRequestId/auth-url')
  async generateGoogleAuthUrl(@Param() param) {
    return this.socialAuthService.generateGoogleAuthUrl(param.botRequestId);
  }

  @Get('/google/callback')
  async googleCallback(@Param() param, @Query() query, @Response() res) {
    const frontendUrl = process.env.FRONT_END_URL;

    try {
      await this.socialAuthService.handleGoogleCallback(
        query.state,
        query.code,
      );

      res.redirect(
        `${frontendUrl}/apps/bot?calendarIntegration=true&botRequestId=${query.state}`,
      );
    } catch (error) {
      Logger.error(error);
      res.redirect(`${frontendUrl}/apps/bot?calendarIntegration=false`);
    }
  }

  @Get('/facebook/callback')
  async facebookCallback(@Param() param, @Query() query, @Response() res) {
    const frontendUrl = `${process.env.FRONT_END_URL}/apps/bot`;
    try {
      console.log('Facebook callback', query);
      await this.socialAuthService.exchangeCodeForTokens(
        query.code,
        query.state,
      );

      res.redirect(
        `${frontendUrl}?instagramIntegration=true&botRequestId=${query.state}`,
      );
    } catch (error) {
      Logger.error(error);
      res.redirect(`${frontendUrl}?instagramIntegration=false`);
    }
  }
}
