import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ber, IsString, IsUUID } from 'class-validator';

export class CompleteInstagramIntegrationDto {
  @IsString()
  token: string;

  @IsString()
  botRequestId: string;

  @IsString()
  accountId: string;

  @IsString()
  accountName: string;
}

export class GetAllIntegrationDto {}

export class UpdateSocialAuthSelected {
  @IsUUID()
  socialAuthId: string;

  @IsString()
  botRequestId: string;
}
