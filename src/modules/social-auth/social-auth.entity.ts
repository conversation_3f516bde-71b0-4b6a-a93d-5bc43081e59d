import {
  CreateDateColumn,
  Entity,
  JoinColumn,
  OneToMany,
  OneToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { Company } from '../companies/companies.entity';
import { SocialAuthAccount } from './social-auth-account.entity';

@Entity()
export class SocialAuth {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @OneToMany(
    () => SocialAuthAccount,
    (socialAuthAccount) => socialAuthAccount.socialAuth,
    { eager: true, nullable: true },
  )
  @JoinColumn()
  socialAuthAccount: SocialAuthAccount[];

  @OneToOne(() => Company, (company) => company.socialAuth, { eager: true })
  @JoinColumn()
  company: Company;

  @CreateDateColumn()
  createAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
