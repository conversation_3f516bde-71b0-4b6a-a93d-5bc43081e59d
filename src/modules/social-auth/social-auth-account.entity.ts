import {
  <PERSON>umn,
  CreateDate<PERSON><PERSON>umn,
  <PERSON><PERSON><PERSON>,
  <PERSON>in<PERSON><PERSON>umn,
  ManyToOne,
  OneToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { ChatProvider } from '../../utils/enums/chatProvider';
import { BotCompany } from '../bot-company/bot-company.entity';
import { SocialAuthType, SocialAuthStatus } from './social-auth.interface';
import { SocialAuth } from './social-auth.entity';
import { Calendars } from '../calendars/calendars.entity';

@Entity()
export class SocialAuthAccount {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ unique: true, nullable: false })
  accountId: string;

  @Column()
  accountName: string;

  @Column({ enum: SocialAuthType })
  type: SocialAuthType;

  @Column({ enum: ChatProvider })
  provider: ChatProvider;

  @Column({ enum: SocialAuthStatus })
  status: SocialAuthStatus;

  @Column({ nullable: true })
  refreshToken: string;

  @ManyToOne(() => SocialAuth, (socialAuth) => socialAuth.socialAuthAccount, {
    nullable: false,
  })
  socialAuth: SocialAuth;

  @ManyToOne(() => BotCompany, (botCompany) => botCompany.selectedSocialAuth, {
    eager: true,
    nullable: true,
  })
  @JoinColumn()
  selectedBotCompany: BotCompany;

  @Column()
  token: string;

  @OneToOne(() => Calendars, (calendar) => calendar.socialAuthAccount, {
    nullable: true,
    cascade: true,
    eager: true,
  })
  calendar: Calendars;

  @CreateDateColumn()
  createAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
