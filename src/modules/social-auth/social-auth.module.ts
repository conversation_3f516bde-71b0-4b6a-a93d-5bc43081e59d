import { forwardRef, Module } from '@nestjs/common';
import { SocialAuthController } from './social-auth.controller';
import { SocialAuthService } from './social-auth.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import { SocialAuth } from './social-auth.entity';
import { BotCompanyModule } from '../bot-company/bot-company.module';
import { CalendarsModule } from '../calendars/calendars.module';
import { FacebookModule } from '../facebook/facebook.module';
import { SocialAuthAccount } from './social-auth-account.entity';
import { Calendars } from '../calendars/calendars.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([SocialAuth, SocialAuthAccount, Calendars]),
    forwardRef(() => CalendarsModule),
    forwardRef(() => FacebookModule),
    forwardRef(() => BotCompanyModule),
  ],
  controllers: [SocialAuthController],
  providers: [SocialAuthService],
  exports: [SocialAuthService],
})
export class SocialAuthModule {}
