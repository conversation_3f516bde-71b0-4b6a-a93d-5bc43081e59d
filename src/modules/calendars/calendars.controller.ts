import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Put,
  Query,
  UseGuards,
} from '@nestjs/common';
import { CalendarsService } from './calendars.service';
import { CompleteCalendarIntegrationDto, GetEvents } from './calendars.dto';
import { AuthGuard } from '../auth/auth.guard';

@UseGuards(AuthGuard)
@Controller('calendars')
export class CalendarsController {
  constructor(private calendarsService: CalendarsService) {}

  @Get('list')
  getCalendarsByBotRequestId(@Query('botRequestId') botRequestId: string) {
    return this.calendarsService.getCreatedCalendarFromGoogle(botRequestId);
  }

  @Get('events')
  getEventsByBotRequestId(@Query() query: GetEvents) {
    return this.calendarsService.getEventsByBotRequestId(
      query.botRequestId,
      query.startDate,
      query.endDate,
    );
  }

  @Delete('event')
  cancelEventByDate(@Body() body) {
    return this.calendarsService.cancelEventByDate(
      body.botRequestId,
      body.date,
    );
  }

  @Put('settings/:calendarId')
  updateCalendarSettings(@Param('calendarId') calendarId: string, @Body() body: CompleteCalendarIntegrationDto) {
    return this.calendarsService.updateCalendarSetting(calendarId, body);
  }

  @Put('complete')
  completeCalendarIntegration(@Body() body: CompleteCalendarIntegrationDto) {
    return this.calendarsService.completeCalendarIntegration(
      body.botRequestId,
      body,
    );
  }
}
