import {
  BadRequestException,
  forwardRef,
  Inject,
  Injectable,
  InternalServerErrorException,
  Logger,
  NotFoundException,
} from '@nestjs/common';
import { google, calendar_v3 } from 'googleapis';

import { SocialAuthService } from '../social-auth/social-auth.service';
import { InjectRepository } from '@nestjs/typeorm';
import { Calendars, Events } from './calendars.entity';
import { Between, Repository } from 'typeorm';
import * as moment from 'moment';
import {
  AvailableIntegration,
  SocialAuthType,
} from '../social-auth/social-auth.interface';
import { CALENDAR_TYPE } from './calendar.enum';
import { CompleteCalendarIntegrationDto } from './calendars.dto';
import { SocialAuthAccount } from '../social-auth/social-auth-account.entity';
import { SocialAuth } from '../social-auth/social-auth.entity';
import { ChatProvider } from '../../utils/enums/chatProvider';

@Injectable()
export class CalendarsService {
  constructor(
    @Inject(forwardRef(() => SocialAuthService))
    private socialAuthService: SocialAuthService,
    @InjectRepository(Calendars) private calendarsRepo: Repository<Calendars>,
    @InjectRepository(Events) private eventsRepo: Repository<Events>,
  ) {}

  async initGoogleCalendarInstanceClient(
    botRequestId: string,
  ): Promise<calendar_v3.Calendar> {
    const socialAuthInstance: SocialAuth | null =
      await this.socialAuthService.getSocialAuthByBotRequestId(botRequestId);

    if (
      !socialAuthInstance ||
      !socialAuthInstance.socialAuthAccount ||
      socialAuthInstance.socialAuthAccount.length === 0
    ) {
      throw new BadRequestException(
        'Social authentication details not found or no linked accounts for the given botRequestId.',
      );
    }

    const googleCalendarAccount = socialAuthInstance.socialAuthAccount.find(
      (acc) =>
        acc.type === SocialAuthType.CALENDAR &&
        acc.provider === ChatProvider.GOOGLE,
    );

    if (!googleCalendarAccount) {
      throw new BadRequestException(
        'Google Calendar account not found for the given botRequestId.',
      );
    }

    if (!googleCalendarAccount.token) {
      throw new BadRequestException(
        'Access token not found for Google Calendar account.',
      );
    }

    const googleAuth = this.socialAuthService.googleClient;
    googleAuth.setCredentials({
      access_token: googleCalendarAccount.token,
      refresh_token: googleCalendarAccount.refreshToken,
    });

    return google.calendar({ version: 'v3', auth: googleAuth });
  }

  async getGoogleCalendarsByBotRequestId(
    botRequestId: string,
  ): Promise<calendar_v3.Schema$Calendar | null> {
    const googleCalendarClient =
      await this.initGoogleCalendarInstanceClient(botRequestId);

    const calendar = await this.calendarsRepo.findOne({
      where: { botRequestId },
    });

    if (!calendar) {
      return null;
    }

    const googleCalendar = await googleCalendarClient.calendars
      .get({
        calendarId: calendar.calendarId,
      })
      .catch(() => ({ data: null }));

    return googleCalendar.data;
  }

  async getCreatedCalendarFromGoogle(botRequestId: string) {
    const googleCalendarClient =
      await this.initGoogleCalendarInstanceClient(botRequestId);

    const googleCalendar = await googleCalendarClient.calendarList
      .list()
      .catch((err) => {
        this.logger.error(err);
        new InternalServerErrorException();
        return { data: err };
      });

    return googleCalendar.data;
  }

  async handleGoogleCalendarIntegration(
    socialAuthAccount: SocialAuthAccount,
  ): Promise<Calendars> {
    if (!socialAuthAccount.selectedBotCompany) {
      this.logger.error(
        'selectedBotCompany is missing on socialAuthAccount in handleGoogleCalendarIntegration',
      );
      throw new InternalServerErrorException(
        'Cannot process calendar integration without a selected bot company on the social auth account.',
      );
    }

    let calendar = await this.calendarsRepo.findOne({
      where: { calendarId: socialAuthAccount.accountId },
    });

    const calendarDetails = {
      calendarId: socialAuthAccount.accountId,
      email: socialAuthAccount.accountName,
      botRequestId: socialAuthAccount.selectedBotCompany.botRequestId,
      socialAuthAccount: socialAuthAccount,
      type: CALENDAR_TYPE.APPOINTMENT,
      calendarName: socialAuthAccount.accountName,
    };

    if (calendar) {
      // Calendar exists, update it
      this.logger.log(
        `Updating existing calendar for calendarId: ${socialAuthAccount.accountId}`,
      );
      calendar = this.calendarsRepo.merge(calendar, calendarDetails);
    } else {
      // Calendar does not exist, create a new one
      this.logger.log(
        `Creating new calendar for calendarId: ${socialAuthAccount.accountId}`,
      );
      calendar = this.calendarsRepo.create(calendarDetails);
    }

    const savedCalendar = await this.calendarsRepo.save(calendar);
    return savedCalendar;
  }

  async getCalendarSettingByBotRequestId(botRequestId: string) {
    return this.calendarsRepo.findOne({ where: { botRequestId } });
  }

  async addEventToCalendar(
    botRequestId: string,
    date: moment.Moment,
    duration: number,
    details: {
      name: string;
      email: string;
      phone: string;
    },
  ) {
    const googleCalendarClient =
      await this.initGoogleCalendarInstanceClient(botRequestId);

    const biitbotCalendar = await this.calendarsRepo.findOne({
      where: { botRequestId: botRequestId },
    });

    const attendees = [];
    if (details.email) {
      attendees.push({ displayName: details.name, email: details.email });
    }

    try {
      const eventCreated = await googleCalendarClient.events.insert({
        calendarId: biitbotCalendar.calendarId,
        sendNotifications: true,
        requestBody: {
          summary: `Appointment with ${details?.name}`,
          start: {
            dateTime: date.utc().toISOString(),
          },
          end: {
            dateTime: moment(date).add(duration, 'minutes').utc().toISOString(),
          },
          attendees,
        },
      });

      await this.eventsRepo.save({
        calendar: biitbotCalendar,
        customerEmail: details?.email,
        customerName: details.name,
        eventId: eventCreated.data.id,
        meetingDate: date.utc().toDate(),
        isActive: true,
        phoneNumber: details?.phone,
      });

      return true;
    } catch (error) {
      Logger.error(error);
      return false;
    }
  }

  async getEventsByBotRequestId(
    botRequestId: string,
    startDate: string,
    endDate: string,
  ) {
    const googleCalendarClient =
      await this.initGoogleCalendarInstanceClient(botRequestId);
    const biitbotCalendar = await this.calendarsRepo.findOne({
      where: { botRequestId },
    });

    const events = await googleCalendarClient.events.list({
      calendarId: biitbotCalendar.calendarId,
      timeMin: moment(startDate).toISOString(),
      timeMax: moment(endDate).toISOString(),
      singleEvents: true,
      orderBy: 'startTime',
    });

    return events.data;
  }

  async deleteCalendarByBotRequestId(calendarId: number) {
    const calendar = await this.calendarsRepo.findOne({
      where: { id: calendarId },
    });

    if (!calendar) {
      return;
    }

    const googleCalendarClient = await this.initGoogleCalendarInstanceClient(
      calendar.botRequestId,
    );

    await this.calendarsRepo.remove(calendar);

    await googleCalendarClient.calendars
      .delete({
        calendarId: calendar.calendarId,
      })
      .catch((err) => Logger.error('error deleting calendar', err));
  }

  async cancelEventByEventId(botRequestId: string, eventId: string) {
    const googleCalendarClient =
      await this.initGoogleCalendarInstanceClient(botRequestId);

    const biitbotCalendar = await this.calendarsRepo.findOne({
      where: { botRequestId },
    });

    const eventCanceled = await googleCalendarClient.events
      .delete({
        calendarId: biitbotCalendar.calendarId,
        eventId,
      })
      .catch((err) => Logger.error('error deleting event', err));

    const event = await this.eventsRepo.findOne({ where: { eventId } });
    event.isActive = false;
    await this.eventsRepo.save(event);

    return eventCanceled;
  }

  async cancelEventByPhoneNumber(
    botRequestId: string,
    customerDetails: {
      phoneNumber: string;
    },
  ) {
    const googleCalendarClient =
      await this.initGoogleCalendarInstanceClient(botRequestId);

    const biitbotCalendar = await this.calendarsRepo.findOne({
      where: { botRequestId },
    });

    const event = await this.eventsRepo.findOne({
      where: { phoneNumber: customerDetails.phoneNumber },
    });

    if (event) {
      await this.cancelEventByEventId(botRequestId, event.eventId);
      await this.eventsRepo.delete({ eventId: event.eventId });

      return { canceled: true };
    } else {
      console.log('event not found');
      return { canceled: false, details: 'event not found' };
    }
  }

  async cancelEventByDate(botRequestId: string, date: string) {
    const googleCalendarClient =
      await this.initGoogleCalendarInstanceClient(botRequestId);

    const biitbotCalendar = await this.calendarsRepo.findOne({
      where: { botRequestId },
    });

    const startDate = moment(date).toISOString();
    const endDate = moment(date).add(30, 'minutes').toISOString();

    const events = await googleCalendarClient.events.list({
      calendarId: biitbotCalendar.calendarId,
      timeMax: endDate,
      timeMin: startDate,
      singleEvents: false,
    });

    if (events.data.items.length > 0) {
      const eventId = events.data.items[0].id;

      await this.cancelEventByEventId(botRequestId, eventId);

      return { canceled: false };
    }

    throw new BadRequestException('No events found');
  }

  async createEventByBotRequestId(
    botRequestId: string,
    event: calendar_v3.Schema$Event,
  ) {
    const googleCalendarClient =
      await this.initGoogleCalendarInstanceClient(botRequestId);

    const biitbotCalendar = await this.calendarsRepo.findOne({
      where: { botRequestId },
    });

    const eventCreated = await googleCalendarClient.events
      .insert({
        calendarId: biitbotCalendar.calendarId,
        requestBody: event,
      })
      .catch((err) => Logger.error('error creating event', err));

    return eventCreated;
  }

  async isDateAvailable(
    botRequestId: string,
    date: moment.Moment,
    interval: number,
  ) {
    const googleCalendarClient =
      await this.initGoogleCalendarInstanceClient(botRequestId);

    const biitbotCalendar = await this.calendarsRepo.findOne({
      where: { botRequestId },
    });

    const events = await googleCalendarClient.events.list({
      calendarId: biitbotCalendar.calendarId,
      timeMin: date.toISOString(),
      timeMax: date.add(interval, 'minutes').toISOString(),
      singleEvents: false,
    });

    return events.data.items.length === 0 ? true : false;
  }

  async getCallingFunctions() {
    const getCalendarAvailability = {
      type: 'function',
      function: {
        name: 'get_calendar_availability',
        description: 'check if the date of the appointment is available.',
        parameters: {
          type: 'object',
          required: ['date'],
          properties: {
            date: {
              type: 'string',
              description:
                'the date for the appointment, return this value as YYYY-MM-DD HH:mm',
            },
          },
          additionalProperties: false,
        },
      },
    };

    const cancelAppointment = {
      type: 'function',
      function: {
        name: 'cancel_appointment',
        description: 'Cancel an appointment or event',
        strict: true,
        parameters: {
          type: 'object',
          required: ['phoneNumber'],
          properties: {
            phoneNumber: {
              type: 'string',
              description:
                'Telephone number with which the reservation was made.',
            },
          },
          additionalProperties: false,
        },
      },
    };

    const schedule_appointment = {
      type: 'function',
      function: {
        name: 'schedule_appointment',
        description:
          'Schedule an appointment or event. For appointments, ensure availability is confirmed before calling this function. Availability confirmation is not required for events.',
        parameters: {
          type: 'object',
          properties: {
            description: {
              type: 'string',
              description: 'Ask if they want to add any comments',
              nullable: true,
            },
            name: {
              type: 'string',
              description: 'Name of the person scheduling the appointment.',
            },
            email: {
              type: 'string',
              description:
                'Email of the person scheduling the appointment, to send confirmation or notification.',
              nullable: true,
            },
            phone: {
              type: 'string',
              description:
                'Phone number of the person scheduling the appointment, required to complete the booking.',
            },
            date: {
              type: 'string',
              description:
                'The date and time for the appointment or event in YYYY-MM-DD HH:mm format (e.g., 2025-03-30 14:30).',
            },
            type: {
              type: 'string',
              description:
                'Type of schedule, can be event or appointment. Default is appointment.',
            },
            eventName: {
              type: 'string',
              description: 'Event name if the schedule type is event.',
              nullable: true,
            },
          },
          required: ['name', 'phone', 'date', 'type'],
        },
      },
    };

    return [getCalendarAvailability, schedule_appointment, cancelAppointment];
  }

  async updateCalendarSetting(calendarId: string, data: CompleteCalendarIntegrationDto) {
    const calendar = await this.calendarsRepo.findOne({ where: { calendarId } });

    if (!calendar) {
      throw new NotFoundException('Calendar not found');
    }

    calendar.availableHoursStart = data.availableHoursStart;
    calendar.availableHoursEnd = data.availableHoursEnd;
    calendar.availableDays = data.availableDays;
    calendar.intervalValue = data.meetingInterval.value;
    calendar.intervalUnit = data.meetingInterval.unit;
    calendar.calendarName = data.calendarName;


    return this.calendarsRepo.save(calendar);
  }

  async completeCalendarIntegration(
    botRequestId: string,
    data: CompleteCalendarIntegrationDto,
  ) {
    const googleCalendarClient =
      await this.initGoogleCalendarInstanceClient(botRequestId);

    const botCalendar = await this.calendarsRepo.findOne({
      where: { botRequestId },
    });

    if (!googleCalendarClient || !botCalendar) {
      new BadRequestException('no initial integration founded');
    }

    if (data.isExistingCalendar) {
      botCalendar.calendarId = data.calendarId;
    } else {
      const googleCalendarCreated = await googleCalendarClient.calendars.insert(
        {
          requestBody: {
            summary: data.calendarName,
            description: `Calendar created by Biitbot for the bot public key (${botRequestId})`,
            etag: botRequestId,
          },
        },
      );
      botCalendar.calendarId = googleCalendarCreated.data.id;
    }

    if (data.reminderTime.enable) {
      botCalendar.reminderTime = data.reminderTime.value;
    }

    botCalendar.availableHoursStart = data.availableHoursStart;
    botCalendar.availableHoursEnd = data.availableHoursEnd;
    botCalendar.availableDays = data.availableDays;
    botCalendar.intervalValue = data.meetingInterval.value;
    botCalendar.intervalUnit = data.meetingInterval.unit;
    botCalendar.calendarName = data.calendarName;

    const calendarCreated = await this.calendarsRepo.save(botCalendar);

    return { status: 'complete', data: calendarCreated };
  }

  private readonly logger = new Logger(CalendarsService.name);
}
