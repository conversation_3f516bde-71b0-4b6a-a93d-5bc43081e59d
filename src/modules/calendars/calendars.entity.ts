import {
  <PERSON>umn,
  Create<PERSON>ate<PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  ManyToOne,
  OneToMany,
  OneToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { CALENDAR_TYPE } from './calendar.enum';
import { SocialAuthAccount } from '../social-auth/social-auth-account.entity';

@Entity('calendars')
export class Calendars {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  calendarId: string;

  @Column({ nullable: true })
  calendarName: string;

  @Column()
  email: string;

  @Column({ nullable: true })
  intervalValue: number;

  @Column({ enum: ['minutes', 'hours'], nullable: true })
  intervalUnit: string;

  @Column({ nullable: true, enum: [1, 2, 3, 4, 5, 6, 7], type: 'simple-array' })
  availableDays: number[];

  @Column({ nullable: true })
  availableHoursStart: string;

  @Column({ nullable: true })
  availableHoursEnd: string;

  @Column({ enum: [10, 30, 60], nullable: true })
  reminderTime: number;

  @Column({ type: 'enum', enum: CALENDAR_TYPE, nullable: true })
  type: CALENDAR_TYPE;

  @OneToOne(
    () => SocialAuthAccount,
    (socialAuthAccount) => socialAuthAccount.calendar,
    { onDelete: 'CASCADE', nullable: false },
  )
  @JoinColumn()
  socialAuthAccount: SocialAuthAccount;

  @OneToMany(() => Events, (event) => event.calendar)
  events: Events[];

  @Column()
  botRequestId: string;

  @UpdateDateColumn()
  updatedAt: Date;

  @CreateDateColumn()
  createdAt: Date;
}

@Entity('events')
export class Events {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ unique: true })
  eventId: string;

  @Column()
  customerName: string;

  @Column({ nullable: true })
  customerEmail: string;

  @Column()
  phoneNumber: string;

  @Column({
    type: 'timestamptz',
    nullable: false,
    default: () => 'CURRENT_TIMESTAMP',
  })
  meetingDate: Date;

  @Column({ type: 'boolean', default: true })
  isActive: boolean;

  @ManyToOne(() => Calendars, (calendar) => calendar.events)
  @JoinColumn()
  calendar: Calendars;

  @UpdateDateColumn()
  updatedAt: Date;

  @CreateDateColumn()
  createdAt: Date;
}
