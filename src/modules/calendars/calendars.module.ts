import { forwardRef, Module } from '@nestjs/common';
import { CalendarsService } from './calendars.service';
import { CalendarsController } from './calendars.controller';
import { SocialAuthModule } from '../social-auth/social-auth.module';
import { Calendars, Events } from './calendars.entity';
import { TypeOrmModule } from '@nestjs/typeorm';

@Module({
  imports: [
    TypeOrmModule.forFeature([Calendars, Events]),
    forwardRef(() => SocialAuthModule),
  ],
  providers: [CalendarsService],
  controllers: [CalendarsController],
  exports: [CalendarsService],
})
export class CalendarsModule { }
