import {
  IsString,
  IsNotEmpty,
  IsBoolean,
  IsNumber,
  Min,
  IsIn,
  IsOptional,
  ValidateNested,
  IsArray,
  ArrayNotEmpty,
  Validate,
} from 'class-validator';
import { Type, Transform } from 'class-transformer';

export class GetEvents {
  @IsString()
  botRequestId: string;

  @IsString()
  startDate: string;

  @IsString()
  endDate: string;
}

export class CreateEventDto {
  @IsString()
  summary: string;

  @IsString()
  description: string;

  @IsString()
  botRequestId: string;

  @IsString()
  startDate: string;

  @IsString()
  hours: string;
}

// DTO para el intervalo de citas (minutos u horas)
class MeetingIntervalDto {
  @IsNumber()
  @Min(1, { message: 'El intervalo de citas debe ser al menos 1 minuto' })
  @Transform(({ value }) => parseInt(value))
  value: number;

  @IsIn(['minutes', 'hours'], {
    message: "El tipo de intervalo debe ser 'minutes' o 'hours'",
  })
  @Validate(({ value }) => console.log(value))
  unit: 'minutes' | 'hours';
}

// DTO para los recordatorios
class ReminderTimeDto {
  @IsBoolean()
  @Transform(({ value }) => value === 'true')
  enable: boolean;

  @IsOptional()
  @IsIn([10, 30, 60], {
    message: 'El tiempo de recordatorio debe ser 10, 30 o 60 minutos',
  })
  @IsNumber({}, { message: 'El tiempo de recordatorio debe ser un número' })
  @Transform(({ value }) => Number(value))
  value?: number;
}

// DTO Principal
export class CompleteCalendarIntegrationDto {
  @IsString()
  botRequestId: string;

  @IsString()
  @IsOptional()
  calendarId: string;

  @IsBoolean()
  isExistingCalendar: boolean;

  @IsString()
  @IsOptional()
  calendarName: string;

  @ValidateNested()
  @Type(() => MeetingIntervalDto)
  meetingInterval: MeetingIntervalDto;

  @IsArray()
  @ArrayNotEmpty({ message: 'Debes seleccionar al menos un día disponible' })
  @IsIn([1, 2, 3, 4, 5, 6, 7], { each: true })
  availableDays: number[];

  @IsString({ message: 'La hora de inicio debe estar en formato HH:MM' })
  availableHoursStart: string;

  @IsString({ message: 'La hora de fin debe estar en formato HH:MM' })
  availableHoursEnd: string;

  @ValidateNested()
  @Type(() => ReminderTimeDto)
  reminderTime: ReminderTimeDto;
}
