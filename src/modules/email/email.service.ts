import { Injectable, Logger } from '@nestjs/common';
import { Resend } from 'resend';
import { verificationEmailTemplate } from './template/mail-verify';
import { ResetPassword } from './template/reset-password';
import { welcome } from './template/welcome';

@Injectable()
export class EmailService {
  private connectionString: string;
  private emailClient: any;

  constructor() {
    // this.connectionString = `endpoint=https://biitbot-email-resoruce.unitedstates.communication.azure.com/;accesskey=U/m+lgqNjMrPKkNFfq+uEm/2zaKHweP3xFdOOgP4GbFbZtQDIYVTwDSbY5sg5WfqHTn1nbr6KJg1jMnuyigTxA==`;
    this.emailClient = new Resend(process.env.RESEND_API_KEY);
  }

  async sendEmail(to: string, subject: string, template: string) {
    const response = await this.emailClient.emails.send({
      from: '<EMAIL>',
      to,
      subject,
      html: template,
    });
    Logger.debug(response);

    return response;
    // const poller = await this.emailClient.beginSend({
    //   senderAddress: '<EMAIL>',
    //   content: {
    //     subject: subject,
    //     html: template,
    //   },
    //   recipients: {
    //     to: [
    //       {
    //         address: to,
    //       },
    //     ],
    //   },
    // });
    // const response = await poller.pollUntilDone();
    // Logger.debug(response);
    // return response;
  }

  async sendVerificationEmail(to: string, token: string, clientName: string) {
    const subject = 'Verify your email';
    const url = `${process.env.BASE_DOMAIN}/auth/verify-email/${token}`;
    const EMAIL_METADATA = {
      CLIENT_NAME: clientName,
      VERIFICATION_URL: url,
      SUPPORT_EMAIL: '<EMAIL>',
    };
    return this.sendEmail(
      to,
      subject,
      verificationEmailTemplate(EMAIL_METADATA),
    );
  }

  async sendWelcomeEmail(to: string) {
    const subject = 'Bienvendio a Biitbot';
    return this.sendEmail(to, subject, welcome());
  }

  async sendResetPasswordEmail(to: string, token: string) {
    const url = `${process.env.APP_URL}/reset-password?token=${token}`;
    const subject = 'Cambiar contraseña';
    return this.sendEmail(to, subject, ResetPassword({ RESET_URL: url }));
  }
}
