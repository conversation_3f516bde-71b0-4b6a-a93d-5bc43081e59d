import {
  BadRequestException,
  Injectable,
  Logger,
  NotFoundException,
  UnauthorizedException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { JwtService } from '@nestjs/jwt';
import { ConfigService } from '@nestjs/config';
import * as moment from 'moment';
import { EventEmitter2, OnEvent } from '@nestjs/event-emitter';

import { Auth } from './auth.entity';
import { Company } from '../companies/companies.entity';
import { Employee } from '../employee/employee.entity';
import { EmployeeService } from '../employee/employee.service';
import { EmailService } from '../email/email.service';
import { SignupDto } from './auth.dto';
import { TOKEN_PAYLOAD } from './auth.interface';

@Injectable()
export class AuthService {
  private readonly logger = new Logger(AuthService.name);

  constructor(
    @InjectRepository(Auth) private authRepo: Repository<Auth>,
    @InjectRepository(Company) private companyRepo: Repository<Company>,
    private jwtService: JwtService,
    private configService: ConfigService,
    private employeeService: EmployeeService,
    private emailService: EmailService,
    private eventEmitter: EventEmitter2,
  ) {}
  async validateUser(email: string, password: string): Promise<Employee> {
    try {
      this.validateLoginInputs(email, password);

      const employee = await this.employeeService.getEmployeeByEmailAndPassword(
        email,
        password,
      );

      this.validateEmployeeAuth(employee, email);
      this.validateEmailConfirmation(employee, email);

      this.logger.log(`Successful login for user: ${email}`);
      return employee;
    } catch (error) {
      this.logFailedLoginAttempt(email, error.message);
      throw error;
    }
  }

  private validateLoginInputs(email: string, password: string): void {
    if (!email || !password) {
      throw new BadRequestException('Email and password are required');
    }

    if (!this.isValidEmailFormat(email)) {
      throw new BadRequestException('Invalid email format');
    }

    if (password.length < 6) {
      throw new BadRequestException(
        'Password must be at least 6 characters long',
      );
    }
  }

  private isValidEmailFormat(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  private validateEmployeeAuth(employee: Employee, email: string): void {
    if (!employee?.auth) {
      throw new NotFoundException('Invalid credentials');
    }
  }

  private validateEmailConfirmation(employee: Employee, email: string): void {
    if (!employee.auth.isEmailConfirmed) {
      throw new UnauthorizedException(
        'Email not confirmed. Please check your email and confirm your account',
      );
    }
  }

  private logFailedLoginAttempt(email: string, reason: string): void {
    this.logger.warn(
      `Failed login attempt for email: ${email}. Reason: ${reason}`,
    );
  }

  async userExist(email: string) {
    const userAuth = await this.employeeService.getEmployeeByEmail(email);

    if (userAuth) {
      return true;
    }

    return false;
  }

  async login(email: string, password: string) {
    const validatedEmployee = await this.validateCredentials(email, password);
    const accessToken = await this.generateAccessToken(validatedEmployee);
    const loginResponse = this.buildLoginResponse(
      validatedEmployee,
      accessToken,
    );

    return loginResponse;
  }

  private async validateCredentials(
    email: string,
    password: string,
  ): Promise<Employee> {
    const employee = await this.validateUser(email, password);
    this.sanitizeEmployeeData(employee);
    return employee;
  }

  private async generateAccessToken(employee: Employee): Promise<string> {
    return await this.createLoginUserPayload(employee);
  }

  private sanitizeEmployeeData(employee: Employee): void {
    delete employee.password;
  }

  private buildLoginResponse(employee: Employee, accessToken: string) {
    const tokenPayload: TOKEN_PAYLOAD = this.jwtService.verify(accessToken);
    const userData = this.buildUserData(employee);

    return {
      userData,
      shouldUpdatePassword: tokenPayload.shouldUpdatePassword,
      accessToken,
      refreshToken: accessToken, // TODO: Implement proper refresh token in Phase 3
    };
  }

  private buildUserData(employee: Employee) {
    return {
      ...employee,
      ability: this.getDefaultUserAbilities(),
    };
  }

  private getDefaultUserAbilities() {
    return [
      {
        action: 'manage',
        subject: 'all',
      },
    ];
  }

  async sendVerificationEmail(email: string) {
    this.createEmailValidationToken(email);
  }

  async createLoginUserPayload(employee: Employee) {
    const payload: TOKEN_PAYLOAD = {
      sub: employee?.id,
      iat: moment().unix(),
      aud: this.configService.get<string>('auth.frontendUrl'),
      iss: this.configService.get<string>('auth.baseDomain'),
      company: {
        id: employee.company.id,
        name: employee.company.name,
      },
      shouldUpdatePassword: employee.shouldUpdatePassword,
    };

    // Use the configured access token expiration time
    const accessTokenExpiresIn = this.configService.get<string>(
      'auth.accessTokenExpiresIn',
    );
    const token = this.jwtService.sign(payload, {
      expiresIn: accessTokenExpiresIn,
    });

    // Token storage in database removed for security reasons
    // JWT tokens are stateless and should not be stored in database
    this.logger.debug(`Access token generated for user: ${employee.id}`);

    return token;
  }

  @OnEvent('auth.createEmailValidationToken')
  async createEmailValidationTokenEvent(email: string) {
    this.createEmailValidationToken(email);
  }

  async createEmailValidationToken(email: string) {
    const employee = await this.employeeService.getEmployeeByEmail(email);

    if (!employee) {
      throw new NotFoundException('user not found');
    }

    const emailTokenExpiresIn = this.configService.get<string>(
      'auth.emailTokenExpiresIn',
    );
    const token = this.jwtService.sign(
      { sub: employee.auth.id, iat: moment().unix() },
      { expiresIn: emailTokenExpiresIn },
    );

    this.authRepo.update(employee.auth.id, {
      emailConfirmationToken: token,
    });

    await this.emailService.sendVerificationEmail(email, token, employee.name);

    return token;
  }

  async recoveryPassword(email: string) {
    const userAuth = await this.employeeService.getEmployeeByEmail(email);

    if (!userAuth) {
      return;
    }

    const passwordResetTokenExpiresIn = this.configService.get<string>(
      'auth.passwordResetTokenExpiresIn',
    );
    const token = this.jwtService.sign(
      { sub: userAuth.id, iat: moment().unix() },
      { expiresIn: passwordResetTokenExpiresIn },
    );

    Promise.all([this.emailService.sendResetPasswordEmail(email, token)]);
  }

  async changePassword(password: string, token: string) {
    const payload = this.jwtService.verify(token);

    const auth = await this.authRepo.findOne({
      where: { employee: { id: payload.sub } },
    });

    if (!auth) {
      throw new NotFoundException('invalid token');
    }

    await this.employeeService.updateEmployeePassword(auth.id, password);
  }

  async signup(user: SignupDto) {
    const employeeExist = await this.employeeService.getEmployeeByEmail(
      user.email,
    );

    if (employeeExist) {
      throw new BadRequestException('User with that email already exist');
    }

    const companyCreated = await this.companyRepo.save({
      phoneNumberVerified: false,
      requireApproval: true,
    });

    const employeeCreated = await this.employeeService.createEmployee({
      ...user,
      companyId: companyCreated.id,
    });

    const token = await this.createEmailValidationToken(employeeCreated.email);

    delete employeeCreated.auth;
    delete employeeCreated.company;

    this.eventEmitter.emit('company.created', {
      companyId: companyCreated.id,
    });

    return {
      user: {
        ...employeeCreated,
        ability: [
          {
            action: 'manage',
            subject: 'all',
          },
        ],
      },
      validateToken: token,
    };
  }

  async validateEmail(token: string) {
    try {
      const payload = this.jwtService.verify(token);
      const auth = await this.authRepo.findOne({
        where: { id: payload.sub },
        relations: ['employee'],
      });

      if (!auth) {
        throw new NotFoundException('user not found');
      }

      await this.authRepo.update(auth.id, { isEmailConfirmed: true });

      await this.emailService.sendWelcomeEmail(auth?.employee?.email);
      return;
    } catch (error) {
      return;
    }
  }

  async validateToken(token: string) {
    try {
      return this.jwtService.verify(token);
    } catch (error) {
      return null;
    }
  }
}
