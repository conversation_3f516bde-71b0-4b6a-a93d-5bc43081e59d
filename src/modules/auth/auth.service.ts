import {
  BadRequestException,
  Injectable,
  NotFoundException,
  UnauthorizedException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { JwtService } from '@nestjs/jwt';
import * as moment from 'moment';
import { EventEmitter2, OnEvent } from '@nestjs/event-emitter';

import { Auth } from './auth.entity';
import { Company } from '../companies/companies.entity';
import { Employee } from '../employee/employee.entity';
import { EmployeeService } from '../employee/employee.service';
import { EmailService } from '../email/email.service';
import { SignupDto } from './auth.dto';
import { TOKEN_PAYLOAD } from './auth.interface';

@Injectable()
export class AuthService {
  constructor(
    @InjectRepository(Auth) private authRepo: Repository<Auth>,
    @InjectRepository(Company) private companyRepo: Repository<Company>,
    private jwtService: JwtService,
    private employeeService: EmployeeService,
    private emailService: EmailService,
    private eventEmitter: EventEmitter2,
  ) {}
  async validateUser(email: string, password: string): Promise<Employee> {
    const employee = await this.employeeService.getEmployeeByEmailAndPassword(
      email,
      password,
    );

    if (!employee?.auth) {
      throw new NotFoundException('invalid credentials');
    }

    if (!employee.auth.isEmailConfirmed) {
      throw new UnauthorizedException('email not validate');
    }

    return employee;
  }

  async userExist(email: string) {
    const userAuth = await this.employeeService.getEmployeeByEmail(email);

    if (userAuth) {
      return true;
    }

    return false;
  }

  async login(email: string, password: string) {
    const userAuth: Employee = await this.validateUser(email, password);

    const token = await this.createLoginUserPayload(userAuth);
    const payload: TOKEN_PAYLOAD = this.jwtService.verify(token);

    delete userAuth.password;

    return {
      userData: {
        ...userAuth,
        ability: [
          {
            action: 'manage',
            subject: 'all',
          },
        ],
      },
      shouldUpdatePassword: payload.shouldUpdatePassword,
      accessToken: token,
      refreshToken: token,
    };
  }

  async sendVerificationEmail(email: string) {
    this.createEmailValidationToken(email);
  }

  async createLoginUserPayload(employee: Employee) {
    const expDay = moment().add(5, 'days').unix();

    const payload: TOKEN_PAYLOAD = {
      sub: employee?.id,
      iat: moment().unix(),
      aud: process.env.FRONT_END_URL,
      iss: process.env.BASE_DOMAIN,
      company: {
        id: employee.company.id,
        name: employee.company.name,
      },
      shouldUpdatePassword: employee.shouldUpdatePassword,
    };
    const token = this.jwtService.sign(payload, { expiresIn: expDay });

    const auth = await this.authRepo.findOne({
      where: { id: employee.auth.id },
    });
    auth.accessToken = token;

    await this.authRepo.save(auth);

    return token;
  }

  @OnEvent('auth.createEmailValidationToken')
  async createEmailValidationTokenEvent(email: string) {
    this.createEmailValidationToken(email);
  }

  async createEmailValidationToken(email: string) {
    const employee = await this.employeeService.getEmployeeByEmail(email);

    if (!employee) {
      throw new NotFoundException('user not found');
    }

    const token = this.jwtService.sign(
      { sub: employee.auth.id, iat: moment().unix() },
      { expiresIn: '1d' },
    );

    this.authRepo.update(employee.auth.id, {
      emailConfirmationToken: token,
    });

    await this.emailService.sendVerificationEmail(email, token, employee.name);

    return token;
  }

  async recoveryPassword(email: string) {
    const userAuth = await this.employeeService.getEmployeeByEmail(email);

    if (!userAuth) {
      return;
    }

    const token = this.jwtService.sign(
      { sub: userAuth.id, iat: moment().unix() },
      { expiresIn: '1d' },
    );

    Promise.all([this.emailService.sendResetPasswordEmail(email, token)]);
  }

  async changePassword(password: string, token: string) {
    const payload = this.jwtService.verify(token);

    const auth = await this.authRepo.findOne({
      where: { employee: { id: payload.sub } },
    });

    if (!auth) {
      throw new NotFoundException('invalid token');
    }

    await this.employeeService.updateEmployeePassword(auth.id, password);
  }

  async signup(user: SignupDto) {
    const employeeExist = await this.employeeService.getEmployeeByEmail(
      user.email,
    );

    if (employeeExist) {
      throw new BadRequestException('User with that email already exist');
    }

    const companyCreated = await this.companyRepo.save({
      phoneNumberVerified: false,
      requireApproval: true,
    });

    const employeeCreated = await this.employeeService.createEmployee({
      ...user,
      companyId: companyCreated.id,
    });

    const token = await this.createEmailValidationToken(employeeCreated.email);

    delete employeeCreated.auth;
    delete employeeCreated.company;

    this.eventEmitter.emit('company.created', {
      companyId: companyCreated.id,
    });

    return {
      user: {
        ...employeeCreated,
        ability: [
          {
            action: 'manage',
            subject: 'all',
          },
        ],
      },
      validateToken: token,
    };
  }

  async validateEmail(token: string) {
    try {
      const payload = this.jwtService.verify(token);
      const auth = await this.authRepo.findOne({
        where: { id: payload.sub },
        relations: ['employee'],
      });

      if (!auth) {
        throw new NotFoundException('user not found');
      }

      await this.authRepo.update(auth.id, { isEmailConfirmed: true });

      await this.emailService.sendWelcomeEmail(auth?.employee?.email);
      return;
    } catch (error) {
      return;
    }
  }

  async validateToken(token: string) {
    try {
      return this.jwtService.verify(token);
    } catch (error) {
      return null;
    }
  }
}
