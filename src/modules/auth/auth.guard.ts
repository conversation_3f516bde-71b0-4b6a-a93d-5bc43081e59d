import {
  CanActivate,
  ExecutionContext,
  Injectable,
  UnauthorizedException,
} from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { Request } from 'express';

import { TOKEN_PAYLOAD } from './auth.interface';
import { AuthService } from './auth.service';

@Injectable()
export class AuthGuard implements CanActivate {
  constructor(
    private jwtService: JwtService,
    private authService: AuthService,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const requestType = context.getType();
    const request = context.switchToHttp().getRequest();
    const pathname = request.path;
    console.log({ pathname });

    if (
      pathname === '/social-auth/google/callback' ||
      pathname === '/social-auth/facebook/callback'
    ) {
      return true;
    }

    let token;

    if (requestType === 'ws') {
      token = request.handshake?.headers.authorization.split(' ')[1];
    } else {
      token = this.extractTokenFromHeader(request);
      if (this.extractTokenFromHeader(request)) {
        token = this.extractTokenFromHeader(request);
      } else {
        token = request.cookies.accessToken;
      }
    }

    if (!token) {
      throw new UnauthorizedException();
    }

    try {
      // Verificar firma y expiración del JWT
      const payload: TOKEN_PAYLOAD = await this.jwtService.verifyAsync(token, {
        secret: process.env.JWT_SECRET_KEY,
      });

      // Verificar si el token está en la blacklist
      const isBlacklisted = await this.authService.isTokenBlacklisted(token);
      if (isBlacklisted) {
        throw new UnauthorizedException('Token has been revoked');
      }

      request['user'] = payload;
      request['token'] = token; // Guardar token para uso en logout
    } catch (error) {
      if (error instanceof UnauthorizedException) {
        throw error;
      }
      throw new UnauthorizedException('Invalid token');
    }
    return true;
  }

  private extractTokenFromHeader(request: Request): string | undefined {
    const [type, token] = request.headers.authorization?.split(' ') ?? [];
    return type === 'Bearer' ? token : undefined;
  }
}
