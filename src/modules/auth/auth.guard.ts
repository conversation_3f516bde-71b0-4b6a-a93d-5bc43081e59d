import {
  CanActivate,
  ExecutionContext,
  Injectable,
  UnauthorizedException,
  Logger,
} from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { Request } from 'express';

import { TOKEN_PAYLOAD } from './auth.interface';
import { TokenBlacklistService } from './token-blacklist.service';

@Injectable()
export class AuthGuard implements CanActivate {
  private readonly logger = new Logger(AuthGuard.name);

  constructor(
    private jwtService: JwtService,
    private tokenBlacklistService: TokenBlacklistService,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const requestType = context.getType();
    const request = context.switchToHttp().getRequest();
    const pathname = request.path;
    console.log({ pathname });

    if (
      pathname === '/social-auth/google/callback' ||
      pathname === '/social-auth/facebook/callback'
    ) {
      return true;
    }

    let token;

    if (requestType === 'ws') {
      token = request.handshake?.headers.authorization.split(' ')[1];
    } else {
      token = this.extractTokenFromHeader(request);
      if (this.extractTokenFromHeader(request)) {
        token = this.extractTokenFromHeader(request);
      } else {
        token = request.cookies.accessToken;
      }
    }

    if (!token) {
      throw new UnauthorizedException('No token provided');
    }

    try {
      // First verify JWT signature and expiration
      const payload: TOKEN_PAYLOAD = await this.jwtService.verifyAsync(token, {
        secret: process.env.JWT_SECRET_KEY,
      });

      // Then check if token is blacklisted
      const isBlacklisted =
        await this.tokenBlacklistService.isTokenBlacklisted(token);
      if (isBlacklisted) {
        this.logger.warn(`Blacklisted token attempted access: ${payload.sub}`);
        throw new UnauthorizedException('Token has been revoked');
      }

      // Token is valid and not blacklisted
      request['user'] = payload;
      request['token'] = token; // Store token for potential logout operations
    } catch (error) {
      if (error instanceof UnauthorizedException) {
        throw error;
      }
      this.logger.error('Token validation failed:', error);
      throw new UnauthorizedException('Invalid token');
    }
    return true;
  }

  private extractTokenFromHeader(request: Request): string | undefined {
    const [type, token] = request.headers.authorization?.split(' ') ?? [];
    return type === 'Bearer' ? token : undefined;
  }
}
