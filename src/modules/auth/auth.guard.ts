import {
  CanActivate,
  ExecutionContext,
  Injectable,
  UnauthorizedException,
} from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { Request } from 'express';

import { TOKEN_PAYLOAD } from './auth.interface';

@Injectable()
export class AuthGuard implements CanActivate {
  constructor(private jwtService: JwtService) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const requestType = context.getType();
    const request = context.switchToHttp().getRequest();
    const pathname = request.path;
    console.log({ pathname });

    if (pathname === '/social-auth/google/callback' || pathname === '/social-auth/facebook/callback') {
      return true;
    }

    let token;

    if (requestType === 'ws') {
      token = request.handshake?.headers.authorization.split(' ')[1];
    } else {
      token = this.extractTokenFromHeader(request);
      if (this.extractTokenFromHeader(request)) {
        token = this.extractTokenFromHeader(request);
      } else {
        token = request.cookies.accessToken;
      }
    }

    if (!token) {
      throw new UnauthorizedException();
    }

    try {
      const payload: TOKEN_PAYLOAD = await this.jwtService.verifyAsync(token, {
        secret: process.env.JWT_SECRET_KEY,
      });
      request['user'] = payload;
    } catch {
      throw new UnauthorizedException();
    }
    return true;
  }

  private extractTokenFromHeader(request: Request): string | undefined {
    const [type, token] = request.headers.authorization?.split(' ') ?? [];
    return type === 'Bearer' ? token : undefined;
  }
}
