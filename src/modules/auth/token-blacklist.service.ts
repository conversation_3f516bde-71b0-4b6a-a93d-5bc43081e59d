import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, LessThan, MoreThan } from 'typeorm';
import { JwtService } from '@nestjs/jwt';
import { TOKEN_PAYLOAD } from './auth.interface';
import { UserToken } from './entities/user-token.entity';
import {
  BlacklistedToken,
  BlacklistReason,
} from './entities/blacklisted-token.entity';

@Injectable()
export class TokenBlacklistService {
  private readonly logger = new Logger(TokenBlacklistService.name);

  constructor(
    @InjectRepository(UserToken)
    private userTokenRepository: Repository<UserToken>,
    @InjectRepository(BlacklistedToken)
    private blacklistedTokenRepository: Repository<BlacklistedToken>,
    private jwtService: JwtService,
  ) {}

  /**
   * Registra un nuevo token activo
   */
  async registerToken(
    token: string,
    userId: number,
    metadata?: { deviceInfo?: string; ipAddress?: string },
  ): Promise<UserToken> {
    try {
      const payload = this.jwtService.decode(token) as TOKEN_PAYLOAD;
      if (!payload || !payload.exp) {
        throw new Error('Invalid token format');
      }

      const tokenId = this.generateTokenId(token);
      const expiresAt = new Date(payload.exp * 1000);

      const existingToken = await this.userTokenRepository.findOne({
        where: { tokenId },
      });

      if (existingToken) {
        existingToken.lastUsedAt = new Date();
        existingToken.deviceInfo = metadata?.deviceInfo || existingToken.deviceInfo;
        existingToken.ipAddress = metadata?.ipAddress || existingToken.ipAddress;
        return await this.userTokenRepository.save(existingToken);
      }

      const userToken = new UserToken();
      userToken.tokenId = tokenId;
      userToken.userId = userId;
      userToken.deviceInfo = metadata?.deviceInfo;
      userToken.ipAddress = metadata?.ipAddress;
      userToken.expiresAt = expiresAt;
      userToken.lastUsedAt = new Date();

      return await this.userTokenRepository.save(userToken);
    } catch (error) {
      this.logger.error('Failed to register token:', error);
      throw error;
    }
  }

  /**
   * Add a token to the blacklist
   */
  async blacklistToken(
    token: string,
    userId?: number,
    reason: BlacklistReason = BlacklistReason.LOGOUT,
    metadata?: {
      ipAddress?: string;
      userAgent?: string;
      additionalInfo?: Record<string, any>;
    },
  ): Promise<void> {
    try {
      const payload = this.jwtService.decode(token) as TOKEN_PAYLOAD;
      if (!payload || !payload.exp) {
        throw new Error('Invalid token format');
      }

      const tokenId = this.generateTokenId(token);
      const expiresAt = new Date(payload.exp * 1000);

      if (expiresAt <= new Date()) {
        this.logger.debug('Token already expired, not adding to blacklist');
        return;
      }

      const existingBlacklist = await this.blacklistedTokenRepository.findOne({
        where: { tokenId },
      });

      if (existingBlacklist) {
        this.logger.debug(`Token already blacklisted: ${tokenId}`);
        return;
      }

      const blacklistedToken = BlacklistedToken.create(
        tokenId,
        userId || payload.sub,
        expiresAt,
        reason,
        metadata,
      );

      await this.blacklistedTokenRepository.save(blacklistedToken);
      await this.userTokenRepository.update({ tokenId }, { isActive: false });

      this.logger.debug(`Token blacklisted successfully: ${tokenId}`);
    } catch (error) {
      this.logger.error('Failed to blacklist token:', error);
      throw error;
    }
  }

  /**
   * Check if a token is blacklisted
   */
  async isTokenBlacklisted(token: string): Promise<boolean> {
    try {
      const tokenId = this.generateTokenId(token);
      const blacklistedToken = await this.blacklistedTokenRepository.findOne({
        where: { tokenId },
      });
      return blacklistedToken !== null;
    } catch (error) {
      this.logger.error('Failed to check token blacklist status:', error);
      return false;
    }
  }

  /**
   * Blacklist all tokens for a specific user
   */
  async blacklistAllUserTokens(
    userId: number,
    reason: BlacklistReason = BlacklistReason.LOGOUT_ALL,
    metadata?: {
      ipAddress?: string;
      userAgent?: string;
      additionalInfo?: Record<string, any>;
    },
  ): Promise<number> {
    try {
      const activeTokens = await this.userTokenRepository.find({
        where: { 
          userId, 
          isActive: true,
          expiresAt: MoreThan(new Date()),
        },
      });

      if (activeTokens.length === 0) {
        this.logger.debug(`No active tokens found for user ${userId}`);
        return 0;
      }

      const blacklistEntries = activeTokens.map(token =>
        BlacklistedToken.create(token.tokenId, userId, token.expiresAt, reason, metadata),
      );

      await this.blacklistedTokenRepository.save(blacklistEntries);
      await this.userTokenRepository.update({ userId, isActive: true }, { isActive: false });

      this.logger.log(`Blacklisted ${activeTokens.length} tokens for user ${userId}`);
      return activeTokens.length;
    } catch (error) {
      this.logger.error(`Failed to blacklist all tokens for user ${userId}:`, error);
      throw error;
    }
  }

  /**
   * Get blacklist statistics
   */
  async getBlacklistStats(): Promise<{
    totalBlacklistedTokens: number;
    activeTokens: number;
    expiredTokens: number;
  }> {
    try {
      const [blacklistedCount, activeCount, expiredCount] = await Promise.all([
        this.blacklistedTokenRepository.count(),
        this.userTokenRepository.count({
          where: { isActive: true, expiresAt: MoreThan(new Date()) },
        }),
        this.userTokenRepository.count({
          where: { expiresAt: LessThan(new Date()) },
        }),
      ]);

      return {
        totalBlacklistedTokens: blacklistedCount,
        activeTokens: activeCount,
        expiredTokens: expiredCount,
      };
    } catch (error) {
      this.logger.error('Failed to get blacklist stats:', error);
      return { totalBlacklistedTokens: 0, activeTokens: 0, expiredTokens: 0 };
    }
  }

  /**
   * Clean up expired tokens (maintenance task)
   */
  async cleanupExpiredTokens(): Promise<{
    expiredTokensRemoved: number;
    expiredBlacklistRemoved: number;
  }> {
    try {
      const now = new Date();
      
      const expiredTokensResult = await this.userTokenRepository.delete({
        expiresAt: LessThan(now),
      });

      const expiredBlacklistResult = await this.blacklistedTokenRepository.delete({
        expiresAt: LessThan(now),
      });

      const expiredTokensRemoved = expiredTokensResult.affected || 0;
      const expiredBlacklistRemoved = expiredBlacklistResult.affected || 0;

      if (expiredTokensRemoved > 0 || expiredBlacklistRemoved > 0) {
        this.logger.log(
          `Cleanup: ${expiredTokensRemoved} expired tokens, ${expiredBlacklistRemoved} expired blacklist entries removed`,
        );
      }

      return { expiredTokensRemoved, expiredBlacklistRemoved };
    } catch (error) {
      this.logger.error('Failed to cleanup expired tokens:', error);
      return { expiredTokensRemoved: 0, expiredBlacklistRemoved: 0 };
    }
  }

  /**
   * Generate a unique token ID from JWT token
   */
  private generateTokenId(token: string): string {
    return token.slice(-32);
  }

  /**
   * Health check for database connection
   */
  async healthCheck(): Promise<boolean> {
    try {
      await this.userTokenRepository.count();
      await this.blacklistedTokenRepository.count();
      return true;
    } catch (error) {
      this.logger.error('Database health check failed:', error);
      return false;
    }
  }
}
