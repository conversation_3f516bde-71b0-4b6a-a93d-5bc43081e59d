import { Injectable, Logger, OnModuleDestroy } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { createClient, RedisClientType } from 'redis';
import { JwtService } from '@nestjs/jwt';
import { TOKEN_PAYLOAD } from './auth.interface';

@Injectable()
export class TokenBlacklistService implements OnModuleDestroy {
  private readonly logger = new Logger(TokenBlacklistService.name);
  private redisClient: RedisClientType;
  private readonly BLACKLIST_PREFIX = 'blacklist:token:';
  private readonly USER_TOKENS_PREFIX = 'user:tokens:';

  constructor(
    private configService: ConfigService,
    private jwtService: JwtService,
  ) {
    this.initializeRedis();
  }

  private async initializeRedis(): Promise<void> {
    try {
      const redisConfig = this.configService.get('auth.redis');
      
      this.redisClient = createClient({
        socket: {
          host: redisConfig.host,
          port: redisConfig.port,
        },
        password: redisConfig.password,
        database: redisConfig.db,
      });

      this.redisClient.on('error', (error) => {
        this.logger.error('Redis connection error:', error);
      });

      this.redisClient.on('connect', () => {
        this.logger.log('Connected to Redis successfully');
      });

      await this.redisClient.connect();
    } catch (error) {
      this.logger.error('Failed to initialize Redis:', error);
      throw error;
    }
  }

  async onModuleDestroy(): Promise<void> {
    if (this.redisClient) {
      await this.redisClient.quit();
      this.logger.log('Redis connection closed');
    }
  }

  /**
   * Add a token to the blacklist
   * @param token JWT token to blacklist
   * @param userId User ID (optional, for user-specific operations)
   */
  async blacklistToken(token: string, userId?: number): Promise<void> {
    try {
      const payload = this.jwtService.decode(token) as TOKEN_PAYLOAD;
      if (!payload || !payload.exp) {
        throw new Error('Invalid token format');
      }

      const tokenId = this.generateTokenId(token);
      const expirationTime = payload.exp * 1000; // Convert to milliseconds
      const currentTime = Date.now();
      const ttl = Math.max(0, Math.floor((expirationTime - currentTime) / 1000));

      if (ttl <= 0) {
        this.logger.debug('Token already expired, not adding to blacklist');
        return;
      }

      // Add token to blacklist with TTL
      await this.redisClient.setEx(
        `${this.BLACKLIST_PREFIX}${tokenId}`,
        ttl,
        JSON.stringify({
          userId: userId || payload.sub,
          blacklistedAt: new Date().toISOString(),
          expiresAt: new Date(expirationTime).toISOString(),
        }),
      );

      // Track user tokens for bulk operations
      if (userId || payload.sub) {
        const userTokensKey = `${this.USER_TOKENS_PREFIX}${userId || payload.sub}`;
        await this.redisClient.sAdd(userTokensKey, tokenId);
        await this.redisClient.expire(userTokensKey, ttl);
      }

      this.logger.debug(`Token blacklisted successfully: ${tokenId}`);
    } catch (error) {
      this.logger.error('Failed to blacklist token:', error);
      throw error;
    }
  }

  /**
   * Check if a token is blacklisted
   * @param token JWT token to check
   * @returns true if token is blacklisted, false otherwise
   */
  async isTokenBlacklisted(token: string): Promise<boolean> {
    try {
      const tokenId = this.generateTokenId(token);
      const result = await this.redisClient.get(`${this.BLACKLIST_PREFIX}${tokenId}`);
      return result !== null;
    } catch (error) {
      this.logger.error('Failed to check token blacklist status:', error);
      // In case of Redis error, allow the request to proceed
      // This prevents Redis outages from breaking authentication
      return false;
    }
  }

  /**
   * Blacklist all tokens for a specific user
   * @param userId User ID
   */
  async blacklistAllUserTokens(userId: number): Promise<void> {
    try {
      const userTokensKey = `${this.USER_TOKENS_PREFIX}${userId}`;
      const tokenIds = await this.redisClient.sMembers(userTokensKey);

      if (tokenIds.length === 0) {
        this.logger.debug(`No tokens found for user ${userId}`);
        return;
      }

      // Blacklist all user tokens
      const pipeline = this.redisClient.multi();
      
      for (const tokenId of tokenIds) {
        const blacklistKey = `${this.BLACKLIST_PREFIX}${tokenId}`;
        pipeline.setEx(
          blacklistKey,
          3600, // 1 hour TTL for bulk blacklisted tokens
          JSON.stringify({
            userId,
            blacklistedAt: new Date().toISOString(),
            reason: 'bulk_logout',
          }),
        );
      }

      // Clear user tokens set
      pipeline.del(userTokensKey);
      
      await pipeline.exec();

      this.logger.log(`Blacklisted ${tokenIds.length} tokens for user ${userId}`);
    } catch (error) {
      this.logger.error(`Failed to blacklist all tokens for user ${userId}:`, error);
      throw error;
    }
  }

  /**
   * Get blacklist statistics
   */
  async getBlacklistStats(): Promise<{
    totalBlacklistedTokens: number;
    activeUsers: number;
  }> {
    try {
      const blacklistKeys = await this.redisClient.keys(`${this.BLACKLIST_PREFIX}*`);
      const userTokenKeys = await this.redisClient.keys(`${this.USER_TOKENS_PREFIX}*`);

      return {
        totalBlacklistedTokens: blacklistKeys.length,
        activeUsers: userTokenKeys.length,
      };
    } catch (error) {
      this.logger.error('Failed to get blacklist stats:', error);
      return {
        totalBlacklistedTokens: 0,
        activeUsers: 0,
      };
    }
  }

  /**
   * Clean up expired tokens (maintenance task)
   */
  async cleanupExpiredTokens(): Promise<number> {
    try {
      const blacklistKeys = await this.redisClient.keys(`${this.BLACKLIST_PREFIX}*`);
      let cleanedCount = 0;

      for (const key of blacklistKeys) {
        const ttl = await this.redisClient.ttl(key);
        if (ttl <= 0) {
          await this.redisClient.del(key);
          cleanedCount++;
        }
      }

      if (cleanedCount > 0) {
        this.logger.log(`Cleaned up ${cleanedCount} expired blacklisted tokens`);
      }

      return cleanedCount;
    } catch (error) {
      this.logger.error('Failed to cleanup expired tokens:', error);
      return 0;
    }
  }

  /**
   * Generate a unique token ID from JWT token
   * @param token JWT token
   * @returns Unique token identifier
   */
  private generateTokenId(token: string): string {
    // Use the last 32 characters of the token as ID
    // This avoids storing the full token while maintaining uniqueness
    return token.slice(-32);
  }

  /**
   * Health check for Redis connection
   */
  async healthCheck(): Promise<boolean> {
    try {
      await this.redisClient.ping();
      return true;
    } catch (error) {
      this.logger.error('Redis health check failed:', error);
      return false;
    }
  }
}
