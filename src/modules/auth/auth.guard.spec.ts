import { Test, TestingModule } from '@nestjs/testing';
import { ExecutionContext, UnauthorizedException } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { ConfigService } from '@nestjs/config';
import { AuthGuard } from './auth.guard';
import { AuthService } from './auth.service';

describe('AuthGuard - Blacklist Tests', () => {
  let guard: AuthGuard;
  let jwtService: jest.Mocked<JwtService>;
  let authService: jest.Mocked<AuthService>;
  let configService: jest.Mocked<ConfigService>;

  const mockJwtService = {
    verifyAsync: jest.fn(),
  };

  const mockAuthService = {
    isTokenBlacklisted: jest.fn(),
  };

  const mockConfigService = {
    get: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        AuthGuard,
        { provide: JwtService, useValue: mockJwtService },
        { provide: AuthService, useValue: mockAuthService },
        { provide: ConfigService, useValue: mockConfigService },
      ],
    }).compile();

    guard = module.get<AuthGuard>(AuthGuard);
    jwtService = module.get(JwtService);
    authService = module.get(AuthService);
    configService = module.get(ConfigService);

    // Setup default mocks
    configService.get.mockReturnValue('test-secret-key');
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  const createMockExecutionContext = (
    authHeader?: string,
  ): ExecutionContext => {
    const mockRequest = {
      headers: authHeader ? { authorization: authHeader } : {},
      path: '/test',
    };

    return {
      getType: jest.fn().mockReturnValue('http'),
      switchToHttp: () => ({
        getRequest: () => mockRequest,
      }),
    } as ExecutionContext;
  };

  describe('canActivate', () => {
    const validToken =
      'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOjEsImV4cCI6MTY5MDAwMDAwMH0.signature';
    const mockPayload = {
      sub: 1,
      email: '<EMAIL>',
      exp: 1690000000,
    };

    it('should allow access with valid non-blacklisted token', async () => {
      // Arrange
      const context = createMockExecutionContext(`Bearer ${validToken}`);
      jwtService.verifyAsync.mockResolvedValue(mockPayload);
      authService.isTokenBlacklisted.mockResolvedValue(false);

      // Act
      const result = await guard.canActivate(context);

      // Assert
      expect(result).toBe(true);
      expect(jwtService.verifyAsync).toHaveBeenCalledWith(validToken, {
        secret: 'test-secret-key',
      });
      expect(authService.isTokenBlacklisted).toHaveBeenCalledWith(validToken);

      const request = context.switchToHttp().getRequest();
      expect(request['user']).toEqual(mockPayload);
      expect(request['token']).toBe(validToken);
    });

    it('should deny access when token is blacklisted', async () => {
      // Arrange
      const context = createMockExecutionContext(`Bearer ${validToken}`);
      jwtService.verifyAsync.mockResolvedValue(mockPayload);
      authService.isTokenBlacklisted.mockResolvedValue(true);

      // Act & Assert
      await expect(guard.canActivate(context)).rejects.toThrow(
        new UnauthorizedException('Token has been revoked'),
      );
      expect(jwtService.verifyAsync).toHaveBeenCalled();
      expect(authService.isTokenBlacklisted).toHaveBeenCalledWith(validToken);
    });

    it('should deny access when no authorization header is present', async () => {
      // Arrange
      const context = createMockExecutionContext();

      // Act & Assert
      await expect(guard.canActivate(context)).rejects.toThrow(
        UnauthorizedException,
      );
      expect(jwtService.verifyAsync).not.toHaveBeenCalled();
      expect(authService.isTokenBlacklisted).not.toHaveBeenCalled();
    });

    it('should deny access when authorization header is malformed', async () => {
      // Arrange
      const context = createMockExecutionContext('InvalidFormat');

      // Act & Assert
      await expect(guard.canActivate(context)).rejects.toThrow(
        UnauthorizedException,
      );
      expect(jwtService.verifyAsync).not.toHaveBeenCalled();
      expect(authService.isTokenBlacklisted).not.toHaveBeenCalled();
    });

    it('should deny access when JWT verification fails', async () => {
      // Arrange
      const context = createMockExecutionContext(`Bearer ${validToken}`);
      jwtService.verifyAsync.mockRejectedValue(new Error('Invalid token'));

      // Act & Assert
      await expect(guard.canActivate(context)).rejects.toThrow(
        UnauthorizedException,
      );
      expect(jwtService.verifyAsync).toHaveBeenCalled();
      expect(authService.isTokenBlacklisted).not.toHaveBeenCalled();
    });

    it('should allow access when blacklist check fails (graceful degradation)', async () => {
      // Arrange
      const context = createMockExecutionContext(`Bearer ${validToken}`);
      jwtService.verifyAsync.mockResolvedValue(mockPayload);
      authService.isTokenBlacklisted.mockRejectedValue(
        new Error('Database error'),
      );

      // Act
      const result = await guard.canActivate(context);

      // Assert
      expect(result).toBe(true); // Should not break authentication on blacklist DB error
      expect(jwtService.verifyAsync).toHaveBeenCalled();
      expect(authService.isTokenBlacklisted).toHaveBeenCalled();

      const request = context.switchToHttp().getRequest();
      expect(request['user']).toEqual(mockPayload);
      expect(request['token']).toBe(validToken);
    });

    it('should handle empty bearer token', async () => {
      // Arrange
      const context = createMockExecutionContext('Bearer ');

      // Act & Assert
      await expect(guard.canActivate(context)).rejects.toThrow(
        UnauthorizedException,
      );
      expect(jwtService.verifyAsync).not.toHaveBeenCalled();
      expect(authService.isTokenBlacklisted).not.toHaveBeenCalled();
    });

    it('should handle bearer token with only spaces', async () => {
      // Arrange
      const context = createMockExecutionContext('Bearer    ');

      // Act & Assert
      await expect(guard.canActivate(context)).rejects.toThrow(
        UnauthorizedException,
      );
      expect(jwtService.verifyAsync).not.toHaveBeenCalled();
      expect(authService.isTokenBlacklisted).not.toHaveBeenCalled();
    });

    it('should use correct JWT secret from config', async () => {
      // Arrange
      const customSecret = 'custom-secret-key';
      configService.get.mockReturnValue(customSecret);
      const context = createMockExecutionContext(`Bearer ${validToken}`);
      jwtService.verifyAsync.mockResolvedValue(mockPayload);
      authService.isTokenBlacklisted.mockResolvedValue(false);

      // Act
      await guard.canActivate(context);

      // Assert
      expect(configService.get).toHaveBeenCalledWith('JWT_SECRET_KEY');
      expect(jwtService.verifyAsync).toHaveBeenCalledWith(validToken, {
        secret: customSecret,
      });
    });
  });
});
