import { Test, TestingModule } from '@nestjs/testing';
import { UnauthorizedException } from '@nestjs/common';
import { AuthController } from './auth.controller';
import { AuthService } from './auth.service';

describe('AuthController - Logout Tests', () => {
  let controller: AuthController;
  let authService: jest.Mocked<AuthService>;

  const mockAuthService = {
    login: jest.fn(),
    blacklistToken: jest.fn(),
    blacklistAllUserTokens: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [AuthController],
      providers: [
        {
          provide: AuthService,
          useValue: mockAuthService,
        },
      ],
    }).compile();

    controller = module.get<AuthController>(AuthController);
    authService = module.get(AuthService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('logout', () => {
    const mockRequest = {
      headers: {
        authorization:
          'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************.signature',
      },
      user: { sub: 1, email: '<EMAIL>' },
    };

    it('should successfully logout with valid token', async () => {
      // Arrange
      authService.blacklistToken.mockResolvedValue(undefined);

      // Act
      const result = await controller.logout(mockRequest);

      // Assert
      expect(result).toEqual({ message: 'Logout successful' });
      expect(authService.blacklistToken).toHaveBeenCalledWith(
        'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************.signature',
        'logout',
      );
    });

    it('should handle logout when no authorization header is present', async () => {
      // Arrange
      const requestWithoutAuth = { headers: {}, user: { sub: 1 } };

      // Act
      const result = await controller.logout(requestWithoutAuth);

      // Assert
      expect(result).toEqual({ message: 'Logout successful' });
      expect(authService.blacklistToken).not.toHaveBeenCalled();
    });

    it('should handle logout when authorization header is malformed', async () => {
      // Arrange
      const requestWithMalformedAuth = {
        headers: { authorization: 'InvalidFormat' },
        user: { sub: 1 },
      };

      // Act
      const result = await controller.logout(requestWithMalformedAuth);

      // Assert
      expect(result).toEqual({ message: 'Logout successful' });
      expect(authService.blacklistToken).not.toHaveBeenCalled();
    });

    it('should handle logout when token extraction fails', async () => {
      // Arrange
      const requestWithEmptyBearer = {
        headers: { authorization: 'Bearer ' },
        user: { sub: 1 },
      };

      // Act
      const result = await controller.logout(requestWithEmptyBearer);

      // Assert
      expect(result).toEqual({ message: 'Logout successful' });
      expect(authService.blacklistToken).not.toHaveBeenCalled();
    });

    it('should handle service errors gracefully', async () => {
      // Arrange
      authService.blacklistToken.mockRejectedValue(new Error('Database error'));

      // Act & Assert
      await expect(controller.logout(mockRequest)).rejects.toThrow(
        'Database error',
      );
      expect(authService.blacklistToken).toHaveBeenCalled();
    });
  });

  describe('logoutAll', () => {
    const mockRequest = {
      headers: {
        authorization:
          'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************.signature',
      },
      user: { sub: 1, email: '<EMAIL>' },
    };

    it('should successfully logout all sessions', async () => {
      // Arrange
      authService.blacklistAllUserTokens.mockResolvedValue(3);

      // Act
      const result = await controller.logoutAll(mockRequest);

      // Assert
      expect(result).toEqual({
        message: 'All sessions logged out successfully',
        tokensInvalidated: 3,
      });
      expect(authService.blacklistAllUserTokens).toHaveBeenCalledWith(
        1,
        'logout_all',
      );
    });

    it('should handle logout all when no tokens are invalidated', async () => {
      // Arrange
      authService.blacklistAllUserTokens.mockResolvedValue(0);

      // Act
      const result = await controller.logoutAll(mockRequest);

      // Assert
      expect(result).toEqual({
        message: 'All sessions logged out successfully',
        tokensInvalidated: 0,
      });
      expect(authService.blacklistAllUserTokens).toHaveBeenCalledWith(
        1,
        'logout_all',
      );
    });

    it('should throw UnauthorizedException when user is not in request', async () => {
      // Arrange
      const requestWithoutUser = {
        headers: { authorization: 'Bearer token' },
      };

      // Act & Assert
      await expect(controller.logoutAll(requestWithoutUser)).rejects.toThrow(
        UnauthorizedException,
      );
      expect(authService.blacklistAllUserTokens).not.toHaveBeenCalled();
    });

    it('should handle service errors gracefully', async () => {
      // Arrange
      authService.blacklistAllUserTokens.mockRejectedValue(
        new Error('Database error'),
      );

      // Act & Assert
      await expect(controller.logoutAll(mockRequest)).rejects.toThrow(
        'Database error',
      );
      expect(authService.blacklistAllUserTokens).toHaveBeenCalled();
    });
  });

  describe('extractTokenFromRequest', () => {
    it('should extract token from valid authorization header', () => {
      // Arrange
      const request = {
        headers: {
          authorization:
            'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************.signature',
        },
      };

      // Act
      const token = controller['extractTokenFromRequest'](request);

      // Assert
      expect(token).toBe(
        'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************.signature',
      );
    });

    it('should return null for missing authorization header', () => {
      // Arrange
      const request = { headers: {} };

      // Act
      const token = controller['extractTokenFromRequest'](request);

      // Assert
      expect(token).toBeNull();
    });

    it('should return null for malformed authorization header', () => {
      // Arrange
      const request = { headers: { authorization: 'InvalidFormat' } };

      // Act
      const token = controller['extractTokenFromRequest'](request);

      // Assert
      expect(token).toBeNull();
    });

    it('should return null for empty bearer token', () => {
      // Arrange
      const request = { headers: { authorization: 'Bearer ' } };

      // Act
      const token = controller['extractTokenFromRequest'](request);

      // Assert
      expect(token).toBeNull();
    });
  });
});
