import {
  Body,
  Controller,
  Get,
  HttpCode,
  Param,
  Post,
  Put,
  Redirect,
} from '@nestjs/common';
import {
  ChangePasswordDto,
  LoginDto,
  RoveryPasswordDto,
  SendVerifyEmailDto,
  SignupDto,
} from './auth.dto';
import { AuthService } from './auth.service';

@Controller('auth')
export class AuthController {
  constructor(private authService: AuthService) {}

  @HttpCode(200)
  @Post('login')
  async login(@Body() body: LoginDto) {
    return this.authService.login(body.email, body.password);
  }

  @Post('logout')
  async logout() {}

  @HttpCode(200)
  @Post('recovery/password')
  async recoveryPassword(@Body() body: RoveryPasswordDto) {
    return this.authService.recoveryPassword(body.email);
  }

  @Put('change/password')
  async changePassword(@Body() body: ChangePasswordDto) {
    return this.authService.changePassword(body.password, body.token);
  }

  @HttpCode(200)
  @Post('signup')
  async signUp(@Body() body: SignupDto) {
    return this.authService.signup(body);
  }

  @Post('send/verify-email')
  async sendVerifyEmail(@Body() body: SendVerifyEmailDto) {
    return this.authService.sendVerificationEmail(body.email);
  }

  @Redirect('https://app.biitbot.com', 302)
  @Get('verify-email/:token')
  async validateEmail(@Param('token') token: string) {
    return this.authService.validateEmail(token);
  }
}
