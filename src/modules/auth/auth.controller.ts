import {
  Body,
  Controller,
  Get,
  HttpCode,
  Param,
  Post,
  Put,
  Redirect,
  UseGuards,
  Request,
} from '@nestjs/common';
import {
  ChangePasswordDto,
  LoginDto,
  RoveryPasswordDto,
  SendVerifyEmailDto,
  SignupDto,
} from './auth.dto';
import { AuthService } from './auth.service';
import { AuthGuard } from './auth.guard';

@Controller('auth')
export class AuthController {
  constructor(private authService: AuthService) {}

  @HttpCode(200)
  @Post('login')
  async login(@Body() body: LoginDto) {
    return this.authService.login(body.email, body.password);
  }

  @HttpCode(200)
  @Post('logout')
  @UseGuards(AuthGuard)
  async logout(@Request() req: any) {
    const token = this.extractTokenFromRequest(req);
    return this.authService.logout(token, req.user?.sub);
  }

  @HttpCode(200)
  @Post('logout-all')
  @UseGuards(AuthGuard)
  async logoutAll(@Request() req: any) {
    return this.authService.logoutAll(req.user?.sub);
  }

  private extractTokenFromRequest(req: any): string {
    const authHeader = req.headers.authorization;
    if (authHeader && authHeader.startsWith('Bearer ')) {
      return authHeader.substring(7);
    }

    // Check for token in cookies as fallback
    if (req.cookies && req.cookies.token) {
      return req.cookies.token;
    }

    throw new Error('No token found in request');
  }

  @HttpCode(200)
  @Post('recovery/password')
  async recoveryPassword(@Body() body: RoveryPasswordDto) {
    return this.authService.recoveryPassword(body.email);
  }

  @Put('change/password')
  async changePassword(@Body() body: ChangePasswordDto) {
    return this.authService.changePassword(body.password, body.token);
  }

  @HttpCode(200)
  @Post('signup')
  async signUp(@Body() body: SignupDto) {
    return this.authService.signup(body);
  }

  @Post('send/verify-email')
  async sendVerifyEmail(@Body() body: SendVerifyEmailDto) {
    return this.authService.sendVerificationEmail(body.email);
  }

  @Redirect('https://app.biitbot.com', 302)
  @Get('verify-email/:token')
  async validateEmail(@Param('token') token: string) {
    return this.authService.validateEmail(token);
  }
}
