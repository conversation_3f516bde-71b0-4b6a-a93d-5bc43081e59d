import {
  Body,
  Controller,
  Get,
  HttpCode,
  Param,
  Post,
  Put,
  Redirect,
  UseGuards,
  Req,
} from '@nestjs/common';
import {
  ChangePasswordDto,
  LoginDto,
  RoveryPasswordDto,
  SendVerifyEmailDto,
  SignupDto,
} from './auth.dto';
import { AuthService } from './auth.service';
import { AuthGuard } from './auth.guard';

@Controller('auth')
export class AuthController {
  constructor(private authService: AuthService) {}

  @HttpCode(200)
  @Post('login')
  async login(@Body() body: LoginDto) {
    return this.authService.login(body.email, body.password);
  }

  @Post('logout')
  @UseGuards(AuthGuard)
  async logout(@Req() request: any) {
    const token = this.extractTokenFromRequest(request);
    if (token) {
      await this.authService.blacklistToken(token, 'logout');
    }
    return { message: 'Logout successful' };
  }

  @Post('logout-all')
  @UseGuards(AuthGuard)
  async logoutAll(@Req() request: any) {
    const user = request.user; // Viene del AuthGuard
    const tokensInvalidated = await this.authService.blacklistAllUserTokens(
      user.sub,
      'logout_all',
    );
    return {
      message: 'All sessions logged out successfully',
      tokensInvalidated,
    };
  }

  @HttpCode(200)
  @Post('recovery/password')
  async recoveryPassword(@Body() body: RoveryPasswordDto) {
    return this.authService.recoveryPassword(body.email);
  }

  @Put('change/password')
  async changePassword(@Body() body: ChangePasswordDto) {
    return this.authService.changePassword(body.password, body.token);
  }

  @HttpCode(200)
  @Post('signup')
  async signUp(@Body() body: SignupDto) {
    return this.authService.signup(body);
  }

  @Post('send/verify-email')
  async sendVerifyEmail(@Body() body: SendVerifyEmailDto) {
    return this.authService.sendVerificationEmail(body.email);
  }

  @Redirect('https://app.biitbot.com', 302)
  @Get('verify-email/:token')
  async validateEmail(@Param('token') token: string) {
    return this.authService.validateEmail(token);
  }

  /**
   * Extrae el token del request (Authorization header o cookies)
   */
  private extractTokenFromRequest(request: any): string | null {
    // Intentar obtener del Authorization header
    const authHeader = request.headers.authorization;
    if (authHeader && authHeader.startsWith('Bearer ')) {
      return authHeader.substring(7);
    }

    // Intentar obtener de cookies
    if (request.cookies && request.cookies.accessToken) {
      return request.cookies.accessToken;
    }

    return null;
  }
}
