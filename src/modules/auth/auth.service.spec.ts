import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { JwtService } from '@nestjs/jwt';
import { ConfigService } from '@nestjs/config';
import { EventEmitter2 } from '@nestjs/event-emitter';
import {
  BadRequestException,
  NotFoundException,
  UnauthorizedException,
} from '@nestjs/common';

import { AuthService } from './auth.service';
import { Auth } from './auth.entity';
import { Company } from '../companies/companies.entity';
import { Employee } from '../employee/employee.entity';
import { EmployeeService } from '../employee/employee.service';
import { EmailService } from '../email/email.service';
import { TOKEN_PAYLOAD } from './auth.interface';

describe('AuthService', () => {
  let service: AuthService;
  let authRepository: jest.Mocked<Repository<Auth>>;
  let companyRepository: jest.Mocked<Repository<Company>>;
  let jwtService: jest.Mocked<JwtService>;
  let configService: jest.Mocked<ConfigService>;
  let employeeService: jest.Mocked<EmployeeService>;
  let emailService: jest.Mocked<EmailService>;
  let eventEmitter: jest.Mocked<EventEmitter2>;

  // Mock data
  const mockEmployee: Employee = {
    id: 1,
    name: 'John Doe',
    email: '<EMAIL>',
    password: 'hashedPassword',
    shouldUpdatePassword: false,
    isDeleted: false,
    canShow: true,
    company: {
      id: 1,
      name: 'Test Company',
    } as Company,
    auth: {
      id: 1,
      isEmailConfirmed: true,
      emailConfirmationToken: null,
      createdAt: new Date(),
      updatedAt: new Date(),
    } as Auth,
    team: null,
    createdAt: new Date(),
    updatedAt: new Date(),
    hashPassword: jest.fn(),
    comparePassword: jest.fn(),
  };

  const mockTokenPayload: TOKEN_PAYLOAD = {
    sub: 1,
    iat: **********,
    aud: 'https://localhost:3001',
    iss: 'https://api.biitbot.com',
    company: {
      id: 1,
      name: 'Test Company',
    },
    shouldUpdatePassword: false,
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        AuthService,
        {
          provide: getRepositoryToken(Auth),
          useValue: {
            findOne: jest.fn(),
            save: jest.fn(),
            update: jest.fn(),
          },
        },
        {
          provide: getRepositoryToken(Company),
          useValue: {
            findOne: jest.fn(),
            save: jest.fn(),
          },
        },
        {
          provide: JwtService,
          useValue: {
            sign: jest.fn(),
            verify: jest.fn(),
          },
        },
        {
          provide: ConfigService,
          useValue: {
            get: jest.fn(),
          },
        },
        {
          provide: EmployeeService,
          useValue: {
            getEmployeeByEmailAndPassword: jest.fn(),
            getEmployeeByEmail: jest.fn(),
            createEmployee: jest.fn(),
            updateEmployeePassword: jest.fn(),
          },
        },
        {
          provide: EmailService,
          useValue: {
            sendVerificationEmail: jest.fn(),
            sendResetPasswordEmail: jest.fn(),
            sendWelcomeEmail: jest.fn(),
          },
        },
        {
          provide: EventEmitter2,
          useValue: {
            emit: jest.fn(),
          },
        },
      ],
    }).compile();

    service = module.get<AuthService>(AuthService);
    authRepository = module.get(getRepositoryToken(Auth));
    companyRepository = module.get(getRepositoryToken(Company));
    jwtService = module.get(JwtService);
    configService = module.get(ConfigService);
    employeeService = module.get(EmployeeService);
    emailService = module.get(EmailService);
    eventEmitter = module.get(EventEmitter2);

    // Setup default config service responses
    configService.get.mockImplementation((key: string) => {
      const config = {
        'auth.accessTokenExpiresIn': '30m',
        'auth.frontendUrl': 'https://localhost:3001',
        'auth.baseDomain': 'https://api.biitbot.com',
        'auth.emailTokenExpiresIn': '24h',
        'auth.passwordResetTokenExpiresIn': '1h',
      };
      return config[key];
    });
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('login', () => {
    const validEmail = '<EMAIL>';
    const validPassword = 'password123';
    const mockAccessToken = 'mock.jwt.token';

    beforeEach(() => {
      employeeService.getEmployeeByEmailAndPassword.mockResolvedValue(
        mockEmployee,
      );
      jwtService.sign.mockReturnValue(mockAccessToken);
      jwtService.verify.mockReturnValue(mockTokenPayload);
    });

    it('should successfully login with valid credentials', async () => {
      // Act
      const result = await service.login(validEmail, validPassword);

      // Assert
      expect(result).toEqual({
        userData: {
          ...mockEmployee,
          password: undefined, // Password should be sanitized
          ability: [
            {
              action: 'manage',
              subject: 'all',
            },
          ],
        },
        shouldUpdatePassword: false,
        accessToken: mockAccessToken,
        refreshToken: mockAccessToken,
      });

      expect(
        employeeService.getEmployeeByEmailAndPassword,
      ).toHaveBeenCalledWith(validEmail, validPassword);
      expect(jwtService.sign).toHaveBeenCalledWith(
        expect.objectContaining({
          sub: mockEmployee.id,
          aud: 'https://localhost:3001',
          iss: 'https://api.biitbot.com',
          company: {
            id: mockEmployee.company.id,
            name: mockEmployee.company.name,
          },
          shouldUpdatePassword: mockEmployee.shouldUpdatePassword,
        }),
        { expiresIn: '30m' },
      );
    });

    it('should throw BadRequestException for missing email', async () => {
      // Act & Assert
      await expect(service.login('', validPassword)).rejects.toThrow(
        new BadRequestException('Email and password are required'),
      );

      expect(
        employeeService.getEmployeeByEmailAndPassword,
      ).not.toHaveBeenCalled();
    });

    it('should throw BadRequestException for missing password', async () => {
      // Act & Assert
      await expect(service.login(validEmail, '')).rejects.toThrow(
        new BadRequestException('Email and password are required'),
      );

      expect(
        employeeService.getEmployeeByEmailAndPassword,
      ).not.toHaveBeenCalled();
    });

    it('should throw BadRequestException for invalid email format', async () => {
      // Act & Assert
      await expect(
        service.login('invalid-email', validPassword),
      ).rejects.toThrow(new BadRequestException('Invalid email format'));

      expect(
        employeeService.getEmployeeByEmailAndPassword,
      ).not.toHaveBeenCalled();
    });

    it('should throw BadRequestException for short password', async () => {
      // Act & Assert
      await expect(service.login(validEmail, '123')).rejects.toThrow(
        new BadRequestException('Password must be at least 6 characters long'),
      );

      expect(
        employeeService.getEmployeeByEmailAndPassword,
      ).not.toHaveBeenCalled();
    });

    it('should throw NotFoundException when employee not found', async () => {
      // Arrange
      employeeService.getEmployeeByEmailAndPassword.mockRejectedValue(
        new BadRequestException('invalid credentials'),
      );

      // Act & Assert
      await expect(service.login(validEmail, validPassword)).rejects.toThrow(
        BadRequestException,
      );
    });

    it('should throw NotFoundException when employee has no auth', async () => {
      // Arrange
      const employeeWithoutAuth = {
        ...mockEmployee,
        auth: null,
        hashPassword: jest.fn(),
        comparePassword: jest.fn(),
      } as Employee;
      employeeService.getEmployeeByEmailAndPassword.mockResolvedValue(
        employeeWithoutAuth,
      );

      // Act & Assert
      await expect(service.login(validEmail, validPassword)).rejects.toThrow(
        new NotFoundException('Invalid credentials'),
      );
    });

    it('should throw UnauthorizedException when email not confirmed', async () => {
      // Arrange
      const unconfirmedEmployee = {
        ...mockEmployee,
        auth: { ...mockEmployee.auth, isEmailConfirmed: false },
        hashPassword: jest.fn(),
        comparePassword: jest.fn(),
      } as Employee;
      employeeService.getEmployeeByEmailAndPassword.mockResolvedValue(
        unconfirmedEmployee,
      );

      // Act & Assert
      await expect(service.login(validEmail, validPassword)).rejects.toThrow(
        new UnauthorizedException(
          'Email not confirmed. Please check your email and confirm your account',
        ),
      );
    });

    it('should sanitize password from employee data', async () => {
      // Act
      const result = await service.login(validEmail, validPassword);

      // Assert
      expect(result.userData.password).toBeUndefined();
    });

    it('should use configured token expiration time', async () => {
      // Act
      await service.login(validEmail, validPassword);

      // Assert
      expect(configService.get).toHaveBeenCalledWith(
        'auth.accessTokenExpiresIn',
      );
      expect(jwtService.sign).toHaveBeenCalledWith(expect.any(Object), {
        expiresIn: '30m',
      });
    });

    it('should include default user abilities', async () => {
      // Act
      const result = await service.login(validEmail, validPassword);

      // Assert
      expect(result.userData.ability).toEqual([
        {
          action: 'manage',
          subject: 'all',
        },
      ]);
    });
  });

  describe('validateUser', () => {
    const validEmail = '<EMAIL>';
    const validPassword = 'password123';

    it('should return employee when validation passes', async () => {
      // Arrange
      employeeService.getEmployeeByEmailAndPassword.mockResolvedValue(
        mockEmployee,
      );

      // Act
      const result = await service.validateUser(validEmail, validPassword);

      // Assert
      expect(result).toEqual(mockEmployee);
      expect(
        employeeService.getEmployeeByEmailAndPassword,
      ).toHaveBeenCalledWith(validEmail, validPassword);
    });

    it('should throw NotFoundException when employee has no auth', async () => {
      // Arrange
      const employeeWithoutAuth = {
        ...mockEmployee,
        auth: null,
        hashPassword: jest.fn(),
        comparePassword: jest.fn(),
      } as Employee;
      employeeService.getEmployeeByEmailAndPassword.mockResolvedValue(
        employeeWithoutAuth,
      );

      // Act & Assert
      await expect(
        service.validateUser(validEmail, validPassword),
      ).rejects.toThrow(new NotFoundException('Invalid credentials'));
    });

    it('should throw UnauthorizedException when email not confirmed', async () => {
      // Arrange
      const unconfirmedEmployee = {
        ...mockEmployee,
        auth: { ...mockEmployee.auth, isEmailConfirmed: false },
        hashPassword: jest.fn(),
        comparePassword: jest.fn(),
      } as Employee;
      employeeService.getEmployeeByEmailAndPassword.mockResolvedValue(
        unconfirmedEmployee,
      );

      // Act & Assert
      await expect(
        service.validateUser(validEmail, validPassword),
      ).rejects.toThrow(
        new UnauthorizedException(
          'Email not confirmed. Please check your email and confirm your account',
        ),
      );
    });
  });

  describe('createLoginUserPayload', () => {
    const mockAccessToken = 'mock.jwt.token';

    beforeEach(() => {
      jwtService.sign.mockReturnValue(mockAccessToken);
    });

    it('should create JWT token with correct payload', async () => {
      // Act
      const result = await service.createLoginUserPayload(mockEmployee);

      // Assert
      expect(result).toBe(mockAccessToken);
      expect(jwtService.sign).toHaveBeenCalledWith(
        expect.objectContaining({
          sub: mockEmployee.id,
          aud: 'https://localhost:3001',
          iss: 'https://api.biitbot.com',
          company: {
            id: mockEmployee.company.id,
            name: mockEmployee.company.name,
          },
          shouldUpdatePassword: mockEmployee.shouldUpdatePassword,
        }),
        { expiresIn: '30m' },
      );
    });

    it('should use configured expiration time', async () => {
      // Arrange
      configService.get.mockReturnValue('15m');

      // Act
      await service.createLoginUserPayload(mockEmployee);

      // Assert
      expect(configService.get).toHaveBeenCalledWith(
        'auth.accessTokenExpiresIn',
      );
      expect(jwtService.sign).toHaveBeenCalledWith(expect.any(Object), {
        expiresIn: '15m',
      });
    });

    it('should include timestamp in payload', async () => {
      // Act
      await service.createLoginUserPayload(mockEmployee);

      // Assert
      expect(jwtService.sign).toHaveBeenCalledWith(
        expect.objectContaining({
          iat: expect.any(Number),
        }),
        expect.any(Object),
      );
    });
  });

  describe('userExist', () => {
    const testEmail = '<EMAIL>';

    it('should return true when user exists', async () => {
      // Arrange
      employeeService.getEmployeeByEmail.mockResolvedValue(mockEmployee);

      // Act
      const result = await service.userExist(testEmail);

      // Assert
      expect(result).toBe(true);
      expect(employeeService.getEmployeeByEmail).toHaveBeenCalledWith(
        testEmail,
      );
    });

    it('should return false when user does not exist', async () => {
      // Arrange
      employeeService.getEmployeeByEmail.mockResolvedValue(null);

      // Act
      const result = await service.userExist(testEmail);

      // Assert
      expect(result).toBe(false);
      expect(employeeService.getEmployeeByEmail).toHaveBeenCalledWith(
        testEmail,
      );
    });
  });

  describe('validateToken', () => {
    const mockToken = 'mock.jwt.token';

    it('should return payload when token is valid', async () => {
      // Arrange
      jwtService.verify.mockReturnValue(mockTokenPayload);

      // Act
      const result = await service.validateToken(mockToken);

      // Assert
      expect(result).toEqual(mockTokenPayload);
      expect(jwtService.verify).toHaveBeenCalledWith(mockToken);
    });

    it('should return null when token is invalid', async () => {
      // Arrange
      jwtService.verify.mockImplementation(() => {
        throw new Error('Invalid token');
      });

      // Act
      const result = await service.validateToken(mockToken);

      // Assert
      expect(result).toBeNull();
    });
  });
});
