import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { JwtService } from '@nestjs/jwt';
import { ConfigService } from '@nestjs/config';
import { EventEmitter2 } from '@nestjs/event-emitter';
import {
  BadRequestException,
  NotFoundException,
  UnauthorizedException,
} from '@nestjs/common';

import { AuthService } from './auth.service';
import { Auth } from './auth.entity';
import { BlacklistedToken } from './blacklisted-token.entity';
import { Company } from '../companies/companies.entity';
import { EmployeeService } from '../employee/employee.service';
import { EmailService } from '../email/email.service';

describe('AuthService', () => {
  let service: AuthService;
  let employeeService: jest.Mocked<EmployeeService>;
  let jwtService: jest.Mocked<JwtService>;
  let configService: jest.Mocked<ConfigService>;

  const mockEmployee = {
    id: 1,
    name: '<PERSON>',
    email: '<EMAIL>',
    password: 'hashedPassword',
    shouldUpdatePassword: false,
    company: { id: 1, name: 'Test Company' },
    auth: { id: 1, isEmailConfirmed: true },
  };

  beforeEach(async () => {
    const mockAuthRepo = {
      findOne: jest.fn(),
      save: jest.fn(),
      update: jest.fn(),
    };

    const mockCompanyRepo = {
      findOne: jest.fn(),
      save: jest.fn(),
    };

    const mockBlacklistedTokenRepo = {
      findOne: jest.fn(),
      save: jest.fn(),
      createQueryBuilder: jest.fn(() => ({
        delete: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        execute: jest.fn().mockResolvedValue({ affected: 0 }),
      })),
    };

    const mockEmployeeService = {
      getEmployeeByEmailAndPassword: jest.fn(),
      getEmployeeByEmail: jest.fn(),
      createEmployee: jest.fn(),
      updateEmployeePassword: jest.fn(),
    };

    const mockJwtService = {
      sign: jest.fn(),
      verify: jest.fn(),
      decode: jest.fn(),
    };

    const mockConfigService = {
      get: jest.fn(),
    };

    const mockEmailService = {
      sendVerificationEmail: jest.fn(),
    };

    const mockEventEmitter = {
      emit: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        AuthService,
        { provide: getRepositoryToken(Auth), useValue: mockAuthRepo },
        { provide: getRepositoryToken(Company), useValue: mockCompanyRepo },
        {
          provide: getRepositoryToken(BlacklistedToken),
          useValue: mockBlacklistedTokenRepo,
        },
        { provide: EmployeeService, useValue: mockEmployeeService },
        { provide: JwtService, useValue: mockJwtService },
        { provide: ConfigService, useValue: mockConfigService },
        { provide: EmailService, useValue: mockEmailService },
        { provide: EventEmitter2, useValue: mockEventEmitter },
      ],
    }).compile();

    service = module.get<AuthService>(AuthService);
    employeeService = module.get(EmployeeService);
    jwtService = module.get(JwtService);
    configService = module.get(ConfigService);

    // Setup default mocks
    configService.get.mockReturnValue('30m');
    jwtService.sign.mockReturnValue('mock.jwt.token');
    jwtService.verify.mockReturnValue({
      sub: 1,
      shouldUpdatePassword: false,
    });
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('login', () => {
    it('should successfully login with valid credentials', async () => {
      // Arrange
      employeeService.getEmployeeByEmailAndPassword.mockResolvedValue(
        mockEmployee as any,
      );

      // Act
      const result = await service.login('<EMAIL>', 'password123');

      // Assert
      expect(result).toBeDefined();
      expect(result.accessToken).toBe('mock.jwt.token');
      expect(result.userData).toBeDefined();
      expect(result.userData.password).toBeUndefined(); // Password should be sanitized
      expect(
        employeeService.getEmployeeByEmailAndPassword,
      ).toHaveBeenCalledWith('<EMAIL>', 'password123');
    });

    it('should throw BadRequestException for missing email', async () => {
      await expect(service.login('', 'password123')).rejects.toThrow(
        BadRequestException,
      );
    });

    it('should throw BadRequestException for missing password', async () => {
      await expect(service.login('<EMAIL>', '')).rejects.toThrow(
        BadRequestException,
      );
    });

    it('should throw BadRequestException for invalid email format', async () => {
      await expect(
        service.login('invalid-email', 'password123'),
      ).rejects.toThrow(BadRequestException);
    });

    it('should throw BadRequestException for short password', async () => {
      await expect(service.login('<EMAIL>', '123')).rejects.toThrow(
        BadRequestException,
      );
    });
  });

  describe('validateUser', () => {
    it('should return employee when validation passes', async () => {
      // Arrange
      employeeService.getEmployeeByEmailAndPassword.mockResolvedValue(
        mockEmployee as any,
      );

      // Act
      const result = await service.validateUser(
        '<EMAIL>',
        'password123',
      );

      // Assert
      expect(result).toEqual(mockEmployee);
    });

    it('should throw NotFoundException when employee has no auth', async () => {
      // Arrange
      const employeeWithoutAuth = { ...mockEmployee, auth: null };
      employeeService.getEmployeeByEmailAndPassword.mockResolvedValue(
        employeeWithoutAuth as any,
      );

      // Act & Assert
      await expect(
        service.validateUser('<EMAIL>', 'password123'),
      ).rejects.toThrow(NotFoundException);
    });

    it('should throw UnauthorizedException when email not confirmed', async () => {
      // Arrange
      const unconfirmedEmployee = {
        ...mockEmployee,
        auth: { ...mockEmployee.auth, isEmailConfirmed: false },
      };
      employeeService.getEmployeeByEmailAndPassword.mockResolvedValue(
        unconfirmedEmployee as any,
      );

      // Act & Assert
      await expect(
        service.validateUser('<EMAIL>', 'password123'),
      ).rejects.toThrow(UnauthorizedException);
    });
  });

  describe('userExist', () => {
    it('should return true when user exists', async () => {
      // Arrange
      employeeService.getEmployeeByEmail.mockResolvedValue(mockEmployee as any);

      // Act
      const result = await service.userExist('<EMAIL>');

      // Assert
      expect(result).toBe(true);
    });

    it('should return false when user does not exist', async () => {
      // Arrange
      employeeService.getEmployeeByEmail.mockResolvedValue(null);

      // Act
      const result = await service.userExist('<EMAIL>');

      // Assert
      expect(result).toBe(false);
    });
  });

  describe('validateToken', () => {
    it('should return payload when token is valid', async () => {
      // Arrange
      const mockPayload = { sub: 1, iat: 123456 };
      jwtService.verify.mockReturnValue(mockPayload);

      // Act
      const result = await service.validateToken('valid.jwt.token');

      // Assert
      expect(result).toEqual(mockPayload);
    });

    it('should return null when token is invalid', async () => {
      // Arrange
      jwtService.verify.mockImplementation(() => {
        throw new Error('Invalid token');
      });

      // Act
      const result = await service.validateToken('invalid.token');

      // Assert
      expect(result).toBeNull();
    });
  });

  // Additional tests for other methods can be added here when needed
  // Currently focusing on core login/validation functionality
});
