import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { Employee } from '../employee/employee.entity';

@Entity('blacklisted_tokens')
export class BlacklistedToken {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ name: 'token_id', length: 64, unique: true })
  tokenId: string; // Últimos caracteres del token para identificación

  @Column({ name: 'user_id' })
  userId: number;

  @Column({ name: 'reason', length: 50, default: 'logout' })
  reason: string; // 'logout', 'logout_all', 'password_change', etc.

  @Column({ name: 'expires_at', type: 'timestamp' })
  expiresAt: Date; // Cuándo expiraría el token originalmente

  @CreateDateColumn({ name: 'blacklisted_at' })
  blacklistedAt: Date;

  // Relación con Employee
  @ManyToOne(() => Employee, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'user_id' })
  user: Employee;

  /**
   * Verifica si el token blacklisteado ha expirado
   */
  isExpired(): boolean {
    return new Date() > this.expiresAt;
  }
}
