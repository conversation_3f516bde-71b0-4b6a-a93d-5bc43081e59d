import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  CreateDateColumn,
  Index,
  JoinColumn,
} from 'typeorm';
import { Employee } from '../../employee/employee.entity';

export enum BlacklistReason {
  LOGOUT = 'logout',
  LOGOUT_ALL = 'logout_all',
  PASSWORD_CHANGE = 'password_change',
  SECURITY_BREACH = 'security_breach',
  ADMIN_REVOKE = 'admin_revoke',
  TOKEN_EXPIRED = 'token_expired',
}

@Entity('blacklisted_tokens')
@Index(['tokenId'], { unique: true })
@Index(['userId'])
@Index(['expiresAt'])
@Index(['reason'])
@Index(['blacklistedAt'])
export class BlacklistedToken {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ name: 'token_id', length: 64, unique: true })
  @Index()
  tokenId: string; // Últimos 32-64 caracteres del token para identificación

  @Column({ name: 'user_id' })
  userId: number;

  @Column({
    name: 'reason',
    type: 'enum',
    enum: BlacklistReason,
    default: BlacklistReason.LOGOUT,
  })
  reason: BlacklistReason;

  @Column({ name: 'expires_at', type: 'timestamp' })
  expiresAt: Date; // Cuando el token original expiraría

  @Column({ name: 'ip_address', length: 45, nullable: true })
  ipAddress: string; // IP desde donde se hizo el logout

  @Column({ name: 'user_agent', type: 'text', nullable: true })
  userAgent: string; // User-Agent del logout

  @Column({ name: 'additional_info', type: 'jsonb', nullable: true })
  additionalInfo: Record<string, any>; // Información adicional en JSON

  @CreateDateColumn({ name: 'blacklisted_at' })
  blacklistedAt: Date;

  // Relación con Employee
  @ManyToOne(() => Employee, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'user_id' })
  user: Employee;

  /**
   * Verifica si el token blacklisteado ha expirado
   * (puede ser eliminado de la blacklist)
   */
  isExpired(): boolean {
    return new Date() > this.expiresAt;
  }

  /**
   * Crea una instancia de BlacklistedToken
   */
  static create(
    tokenId: string,
    userId: number,
    expiresAt: Date,
    reason: BlacklistReason = BlacklistReason.LOGOUT,
    metadata?: {
      ipAddress?: string;
      userAgent?: string;
      additionalInfo?: Record<string, any>;
    },
  ): BlacklistedToken {
    const blacklistedToken = new BlacklistedToken();
    blacklistedToken.tokenId = tokenId;
    blacklistedToken.userId = userId;
    blacklistedToken.reason = reason;
    blacklistedToken.expiresAt = expiresAt;
    blacklistedToken.ipAddress = metadata?.ipAddress;
    blacklistedToken.userAgent = metadata?.userAgent;
    blacklistedToken.additionalInfo = metadata?.additionalInfo;
    return blacklistedToken;
  }

  /**
   * Obtiene una descripción legible de la razón
   */
  getReasonDescription(): string {
    const descriptions = {
      [BlacklistReason.LOGOUT]: 'Usuario cerró sesión',
      [BlacklistReason.LOGOUT_ALL]: 'Usuario cerró todas las sesiones',
      [BlacklistReason.PASSWORD_CHANGE]: 'Cambio de contraseña',
      [BlacklistReason.SECURITY_BREACH]: 'Brecha de seguridad detectada',
      [BlacklistReason.ADMIN_REVOKE]: 'Revocado por administrador',
      [BlacklistReason.TOKEN_EXPIRED]: 'Token expirado',
    };
    return descriptions[this.reason] || 'Razón desconocida';
  }
}
