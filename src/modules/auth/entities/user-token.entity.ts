import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  CreateDateColumn,
  UpdateDateColumn,
  Index,
  JoinColumn,
} from 'typeorm';
import { Employee } from '../../employee/employee.entity';

@Entity('user_tokens')
@Index(['tokenId'], { unique: true })
@Index(['userId', 'isActive'])
@Index(['expiresAt'])
export class UserToken {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ name: 'token_id', length: 64, unique: true })
  @Index()
  tokenId: string; // Últimos 32-64 caracteres del token para identificación

  @Column({ name: 'user_id' })
  userId: number;

  @Column({ name: 'device_info', type: 'text', nullable: true })
  deviceInfo: string; // User-Agent, IP, etc.

  @Column({ name: 'ip_address', length: 45, nullable: true })
  ipAddress: string; // IPv4 o IPv6

  @Column({ name: 'is_active', default: true })
  isActive: boolean;

  @Column({ name: 'expires_at', type: 'timestamp' })
  expiresAt: Date;

  @Column({ name: 'last_used_at', type: 'timestamp', nullable: true })
  lastUsedAt: Date;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  // Relación con Employee
  @ManyToOne(() => Employee, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'user_id' })
  user: Employee;

  /**
   * Marca el token como usado (actualiza lastUsedAt)
   */
  markAsUsed(): void {
    this.lastUsedAt = new Date();
  }

  /**
   * Verifica si el token ha expirado
   */
  isExpired(): boolean {
    return new Date() > this.expiresAt;
  }

  /**
   * Invalida el token
   */
  invalidate(): void {
    this.isActive = false;
  }
}
