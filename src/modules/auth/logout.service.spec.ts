import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { JwtService } from '@nestjs/jwt';
import { AuthService } from './auth.service';
import { BlacklistedToken } from './blacklisted-token.entity';
import { Auth } from './auth.entity';
import { Company } from '../companies/companies.entity';
import { EmployeeService } from '../employee/employee.service';
import { ConfigService } from '@nestjs/config';
import { EmailService } from '../email/email.service';
import { EventEmitter2 } from '@nestjs/event-emitter';

describe('AuthService - Logout Functionality', () => {
  let service: AuthService;
  let jwtService: jest.Mocked<JwtService>;
  let blacklistedTokenRepo: any;

  const mockJwtService = {
    sign: jest.fn(),
    verify: jest.fn(),
    decode: jest.fn(),
  };

  const mockBlacklistedTokenRepo = {
    findOne: jest.fn(),
    save: jest.fn(),
    createQueryBuilder: jest.fn(),
  };

  const mockAuthRepo = {
    findOne: jest.fn(),
    save: jest.fn(),
    create: jest.fn(),
  };

  const mockCompanyRepo = {
    findOne: jest.fn(),
    save: jest.fn(),
  };

  const mockEmployeeService = {
    findByEmail: jest.fn(),
    findById: jest.fn(),
  };

  const mockConfigService = {
    get: jest.fn(),
  };

  const mockEmailService = {
    sendEmail: jest.fn(),
  };

  const mockEventEmitter = {
    emit: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        AuthService,
        { provide: getRepositoryToken(Auth), useValue: mockAuthRepo },
        { provide: getRepositoryToken(Company), useValue: mockCompanyRepo },
        { provide: getRepositoryToken(BlacklistedToken), useValue: mockBlacklistedTokenRepo },
        { provide: EmployeeService, useValue: mockEmployeeService },
        { provide: JwtService, useValue: mockJwtService },
        { provide: ConfigService, useValue: mockConfigService },
        { provide: EmailService, useValue: mockEmailService },
        { provide: EventEmitter2, useValue: mockEventEmitter },
      ],
    }).compile();

    service = module.get<AuthService>(AuthService);
    jwtService = module.get(JwtService);
    blacklistedTokenRepo = module.get(getRepositoryToken(BlacklistedToken));
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('blacklistToken', () => {
    const mockToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************.signature';
    const mockPayload = {
      sub: 1,
      exp: 1690000000,
      iat: 1689000000,
    };

    beforeEach(() => {
      jwtService.decode.mockReturnValue(mockPayload);
    });

    it('should successfully blacklist a valid token', async () => {
      // Arrange
      blacklistedTokenRepo.findOne.mockResolvedValue(null);
      blacklistedTokenRepo.save.mockResolvedValue({});

      // Act
      await service.blacklistToken(mockToken, 'logout');

      // Assert
      expect(jwtService.decode).toHaveBeenCalledWith(mockToken);
      expect(blacklistedTokenRepo.findOne).toHaveBeenCalledWith({
        where: { tokenId: mockToken.slice(-32) },
      });
      expect(blacklistedTokenRepo.save).toHaveBeenCalledWith(
        expect.objectContaining({
          tokenId: mockToken.slice(-32),
          userId: 1,
          reason: 'logout',
          expiresAt: new Date(1690000000 * 1000),
        }),
      );
    });

    it('should not blacklist token if already blacklisted', async () => {
      // Arrange
      blacklistedTokenRepo.findOne.mockResolvedValue({ id: 1 });

      // Act
      await service.blacklistToken(mockToken, 'logout');

      // Assert
      expect(jwtService.decode).toHaveBeenCalledWith(mockToken);
      expect(blacklistedTokenRepo.findOne).toHaveBeenCalled();
      expect(blacklistedTokenRepo.save).not.toHaveBeenCalled();
    });

    it('should throw error for invalid token format', async () => {
      // Arrange
      jwtService.decode.mockReturnValue(null);

      // Act & Assert
      await expect(service.blacklistToken('invalid-token')).rejects.toThrow('Invalid token format');
    });

    it('should use default reason when not provided', async () => {
      // Arrange
      blacklistedTokenRepo.findOne.mockResolvedValue(null);
      blacklistedTokenRepo.save.mockResolvedValue({});

      // Act
      await service.blacklistToken(mockToken);

      // Assert
      expect(blacklistedTokenRepo.save).toHaveBeenCalledWith(
        expect.objectContaining({
          reason: 'logout',
        }),
      );
    });
  });

  describe('isTokenBlacklisted', () => {
    const mockToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************.signature';

    it('should return true if token is blacklisted', async () => {
      // Arrange
      blacklistedTokenRepo.findOne.mockResolvedValue({ id: 1 });

      // Act
      const result = await service.isTokenBlacklisted(mockToken);

      // Assert
      expect(result).toBe(true);
      expect(blacklistedTokenRepo.findOne).toHaveBeenCalledWith({
        where: { tokenId: mockToken.slice(-32) },
      });
    });

    it('should return false if token is not blacklisted', async () => {
      // Arrange
      blacklistedTokenRepo.findOne.mockResolvedValue(null);

      // Act
      const result = await service.isTokenBlacklisted(mockToken);

      // Assert
      expect(result).toBe(false);
    });

    it('should return false if database error occurs', async () => {
      // Arrange
      blacklistedTokenRepo.findOne.mockRejectedValue(new Error('Database error'));

      // Act
      const result = await service.isTokenBlacklisted(mockToken);

      // Assert
      expect(result).toBe(false);
    });
  });

  describe('blacklistAllUserTokens', () => {
    it('should successfully blacklist all user tokens', async () => {
      // Arrange
      const userId = 1;
      blacklistedTokenRepo.save.mockResolvedValue({});

      // Act
      const result = await service.blacklistAllUserTokens(userId, 'logout_all');

      // Assert
      expect(result).toBe(1);
      expect(blacklistedTokenRepo.save).toHaveBeenCalledWith(
        expect.objectContaining({
          tokenId: expect.stringContaining(`user_${userId}_all_`),
          userId: userId,
          reason: 'logout_all',
        }),
      );
    });

    it('should use default reason when not provided', async () => {
      // Arrange
      const userId = 1;
      blacklistedTokenRepo.save.mockResolvedValue({});

      // Act
      const result = await service.blacklistAllUserTokens(userId);

      // Assert
      expect(result).toBe(1);
      expect(blacklistedTokenRepo.save).toHaveBeenCalledWith(
        expect.objectContaining({
          reason: 'logout_all',
        }),
      );
    });
  });

  describe('cleanupExpiredBlacklistedTokens', () => {
    it('should successfully cleanup expired tokens', async () => {
      // Arrange
      const mockQueryBuilder = {
        delete: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        execute: jest.fn().mockResolvedValue({ affected: 5 }),
      };
      blacklistedTokenRepo.createQueryBuilder.mockReturnValue(mockQueryBuilder);

      // Act
      const result = await service.cleanupExpiredBlacklistedTokens();

      // Assert
      expect(result).toBe(5);
      expect(blacklistedTokenRepo.createQueryBuilder).toHaveBeenCalled();
      expect(mockQueryBuilder.delete).toHaveBeenCalled();
      expect(mockQueryBuilder.where).toHaveBeenCalledWith('expires_at < :now', { now: expect.any(Date) });
      expect(mockQueryBuilder.execute).toHaveBeenCalled();
    });

    it('should return 0 if database error occurs', async () => {
      // Arrange
      blacklistedTokenRepo.createQueryBuilder.mockImplementation(() => {
        throw new Error('Database error');
      });

      // Act
      const result = await service.cleanupExpiredBlacklistedTokens();

      // Assert
      expect(result).toBe(0);
    });
  });
});
