import { Injectable } from '@nestjs/common';
import { OnEvent } from '@nestjs/event-emitter';
import { InjectRepository } from '@nestjs/typeorm';
import * as moment from 'moment';
import { Repository } from 'typeorm';

import { Interaction } from '../../utils/enums/events/statistic';
import { getPeakHourPerDay } from '../../utils/helper-function';
import { ChatSessionService } from '../chat-session/chat-session.service';
import { MessageEntryPointService } from '../message-entry-point/message-entry-point.service';
import { OpenAiService } from '../open-ai/open-ai.service';
import { InteractionResponseTime } from './entities/interaction-response-time.entity';
import { Interactions } from './entities/interactions.entity';
import { StatisticUpdateInteraction } from './statistic.interface';
import { ChatResponseGeneratorService } from '../chat-response-generator/chat-response-generator.service';

@Injectable()
export class StatisticService {
  constructor(
    @InjectRepository(Interactions)
    private interactionsRepo: Repository<Interactions>,
    @InjectRepository(InteractionResponseTime)
    private interactionsResponseTime: Repository<InteractionResponseTime>,
    private messageEntryPointService: MessageEntryPointService,
    private chatSessionService: ChatSessionService,
    private openIaService: OpenAiService,
    private chatResponseGeneratorService: ChatResponseGeneratorService,
  ) {}

  async getInteractions(botRequestId: string) {
    const messagesEntryPoint =
      await this.messageEntryPointService.getMessagesEntryPointByBotRequestId(
        botRequestId,
      );

    const chatSessionIds = messagesEntryPoint.map(
      (entryPoint) => entryPoint.chatSession.id,
    );

    if (messagesEntryPoint.length === 0) {
      return [];
    }

    const interactions = await this.interactionsRepo
      .createQueryBuilder('interaction')
      .select('DATE(interaction.createdAt) as date')
      .addSelect('SUM(interaction.numberOfInteractions)', 'totalInteractions')
      .where('interaction.chatSessionId IN (:...chatSessionIds)', {
        chatSessionIds,
      })
      .groupBy('DATE(interaction.createdAt)')
      .getRawMany();

    return interactions;
  }

  async getMessagesAvg(botRequestId: string) {
    const chatHistory =
      await this.chatSessionService.getChatHistoryByBotRequestId(botRequestId);

    if (chatHistory.length === 0) return { messagesAvg: 0 };

    let messages = 0;
    chatHistory.forEach(() => messages++);

    return {
      messagesAvg: Math.floor(messages / chatHistory.length),
    };
  }

  async getInteractionsPeakTime(botRequestId: string) {
    const messagesEntryPoint =
      await this.messageEntryPointService.getMessagesEntryPointByBotRequestId(
        botRequestId,
      );
    const interactions = await Promise.all(
      messagesEntryPoint.map((messageEntryPoint) =>
        this.interactionsRepo.find({
          where: {
            chatSessionId: messageEntryPoint.chatSession.id,
          },
        }),
      ),
    );
    const interactionsWithValue = interactions.flat();

    const interactionDaysAndHours = interactionsWithValue.map(
      (interaction) => ({
        day: moment(interaction.createdAt).format('dddd'),
        hour: moment(interaction.createdAt).format('hh A'),
      }),
    );

    const countByDayAndHour = interactionDaysAndHours.reduce(
      (acc, interaction) => {
        const dateKey = interaction.day;

        if (!acc[dateKey]) {
          acc[dateKey] = { [interaction.hour]: 0 };
        }

        if (!acc[dateKey][interaction.hour]) {
          acc[dateKey][interaction.hour] = 0;
        }

        acc[dateKey][interaction.hour]++;
        return acc;
      },
      {},
    );

    return getPeakHourPerDay(countByDayAndHour);
  }

  async getInteractionAvgLength(botRequestId: string) {
    const messagesEntryPoint =
      await this.messageEntryPointService.getMessagesEntryPointByBotRequestId(
        botRequestId,
      );
    const interactions = await Promise.all(
      messagesEntryPoint.map((messageEntryPoint) =>
        this.interactionsRepo.find({
          where: {
            chatSessionId: messageEntryPoint.chatSession.id,
          },
        }),
      ),
    );

    let interactionsSummed = 0;

    for (const interaction of interactions.flat()) {
      interactionsSummed =
        interactionsSummed + interaction.numberOfInteractions;
    }

    if (interactionsSummed === 0) {
      return {
        interactionAvg: 0,
      };
    }

    const interactionAvg = interactionsSummed / interactions.flat().length;
    return {
      interactionAvg: Math.floor(interactionAvg),
    };
  }

  async getInteractionResponseTimeAvg(botRequestId: string) {
    const messagesEntryPoint =
      await this.messageEntryPointService.getMessagesEntryPointByBotRequestId(
        botRequestId,
      );

    const interactions = await Promise.all(
      messagesEntryPoint.map((messageEntryPoint) =>
        this.interactionsRepo.find({
          where: {
            chatSessionId: messageEntryPoint.chatSession.id,
          },
          relations: ['interactionsResponseTime'],
        }),
      ),
    );

    const interactionsResponseTime = interactions.flat().map((interaction) => ({
      interactionResponseTime: interaction.interactionsResponseTime.map(
        (interactionResponseTime) => interactionResponseTime.time,
      ),
    }));
    let totalInteractionsReponseTime = 0;

    const interactionsResponseTimeTotalSummed = interactionsResponseTime
      .map((v) => v.interactionResponseTime)
      .reduce((acc, current) => {
        let total = 0;

        for (const number of current) {
          totalInteractionsReponseTime = totalInteractionsReponseTime + 1;
          total = total + Number(number);
        }
        return acc + total;
      }, 0);

    if (totalInteractionsReponseTime === 0) {
      return {
        interactionsResponseTimeAvg: 0,
      };
    }

    const totalAvgInMiliseconds =
      interactionsResponseTimeTotalSummed / totalInteractionsReponseTime;

    const totalAvgInSeconds = totalAvgInMiliseconds;

    return {
      interactionsResponseTimeAvg: totalAvgInSeconds.toFixed(2),
    };
  }

  @OnEvent(Interaction.Update)
  async updateInteractions(payload: StatisticUpdateInteraction) {
    const interactionExist = await this.interactionsRepo.findOne({
      where: {
        chatSessionId: payload.chatSessionId,
        role: payload.role,
      },
    });

    const intereactionResponseTimeToSave = new InteractionResponseTime();

    if (!interactionExist) {
      intereactionResponseTimeToSave.time =
        payload.chatResponseGeneratedTimeInSecond;

      const intereactionResponseTimeSaved =
        await this.interactionsResponseTime.save(
          intereactionResponseTimeToSave,
        );

      const interactionToSave = new Interactions();
      interactionToSave.numberOfInteractions = 1;
      interactionToSave.role = payload.role;
      interactionToSave.chatSessionId = payload.chatSessionId;
      interactionToSave.interactionsResponseTime = [
        intereactionResponseTimeSaved,
      ];

      return this.interactionsRepo.save(interactionToSave);
    }

    intereactionResponseTimeToSave.time =
      payload.chatResponseGeneratedTimeInSecond;

    const intereactionResponseTimeSaved =
      await this.interactionsResponseTime.save(intereactionResponseTimeToSave);

    interactionExist.numberOfInteractions =
      interactionExist.numberOfInteractions + 1;
    interactionExist.interactionsResponseTime = [intereactionResponseTimeSaved];
    return this.interactionsRepo.save(interactionExist);
  }

  async getConversationFeeling(botRequestId: string) {
    if (!botRequestId) {
      return {
        feelingAvg: 'neutralidad',
      };
    }

    const chatHistory =
      await this.chatSessionService.getChatHistoryByBotRequestId(botRequestId);

    if (chatHistory.length === 0) {
      return {
        feelingAvg: 'neutralidad',
      };
    }
    const prompts = [
      {
        role: 'system',
        content: `
      Eres un experto analizando conversaciones, tu tarea es analizar
      la siguiente conversación y decirnos como se siente el usuario en la conversación tomando solo un adjetivo de la siguiente lista: contento, satisfecho, feliz, eufórico, optimista, relajado, agradecido, seguro, interesado, motivado, indiferente, neutro, reservado, atento, curioso, paciente, sereno, claro, equilibrado, imparcial, triste, frustrado, enfadado, ansioso, desesperado, confundido, avergonzado, temeroso, pesimista, desmotivado. Solo responde una palabra`,
      },
    ];

    const feeling = await this.openIaService.getResponse(prompts);

    return {
      feelingAvg: feeling.data.choices[0].message.content,
    };
  }
}
