import { Controller, Get, Query, UseGuards } from '@nestjs/common';

import { StatisticService } from './statistic.service';
import { GetStatisticDto } from './dto/get-statistic.dto';
import { AuthGuard } from '../auth/auth.guard';

@UseGuards(AuthGuard)
@Controller('statistic')
export class StatisticController {
  constructor(private readonly statisticService: StatisticService) {}

  @Get('interaction')
  async getInteraction(@Query() param: GetStatisticDto) {
    return this.statisticService.getInteractions(param.botRequestId);
  }

  @Get('interaction-peak')
  async getInteractionPeakTime(@Query() body: GetStatisticDto) {
    return this.statisticService.getInteractionsPeakTime(body.botRequestId);
  }

  @Get('interaction-avg')
  async getInteractionsAvg(@Query() body: GetStatisticDto) {
    return this.statisticService.getInteractionAvgLength(body.botRequestId);
  }

  @Get('interaction-response-time-avg')
  async getInteractionResponseTimeAvg(@Query() body: GetStatisticDto) {
    return this.statisticService.getInteractionResponseTimeAvg(
      body.botRequestId,
    );
  }

  @Get('messages-avg')
  async getChatsLengthAvg(@Query() query: any) {
    return this.statisticService.getMessagesAvg(query.botRequestId);
  }

  @Get('chat-feeling')
  async getChatFeeling(@Query() body) {
    return this.statisticService.getConversationFeeling(body.botRequestId);
  }
}
