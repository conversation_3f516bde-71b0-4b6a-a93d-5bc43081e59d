import { <PERSON>du<PERSON> } from '@nestjs/common';

import { StatisticService } from './statistic.service';
import { StatisticController } from './statistic.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Interactions } from './entities/interactions.entity';
import { MessageEntryPointModule } from '../message-entry-point/message-entry-point.module';
import { InteractionResponseTime } from './entities/interaction-response-time.entity';
import { ChatSessionModule } from '../chat-session/chat-session.module';
import { OpenAiModule } from '../open-ai/open-ai.module';
import { ChatResponseGeneratorModule } from '../chat-response-generator/chat-response-generator.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([Interactions, InteractionResponseTime]),
    MessageEntryPointModule,
    ChatSessionModule,
    OpenAiModule,
    ChatResponseGeneratorModule,
  ],
  controllers: [StatisticController],
  providers: [StatisticService],
  exports: [StatisticService],
})
export class StatisticModule {}
