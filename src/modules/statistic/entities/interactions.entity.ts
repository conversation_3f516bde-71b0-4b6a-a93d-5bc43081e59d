import {
  <PERSON><PERSON><PERSON>,
  CreateDate<PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  OneToMany,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { messageRole } from '../../chat-response-generator/chat-response-generator.enum';
import { InteractionResponseTime } from './interaction-response-time.entity';

@Entity()
export class Interactions {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ type: 'int', nullable: false })
  chatSessionId: number;

  @OneToMany(
    () => InteractionResponseTime,
    (interactionResponseTime) => interactionResponseTime.interactions,
    { nullable: false },
  )
  @JoinColumn()
  interactionsResponseTime: InteractionResponseTime[];

  @Column({ type: 'int' })
  numberOfInteractions: number;

  @Column()
  role: messageRole.USER | messageRole.SYSTEM;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updateAt: Date;
}
