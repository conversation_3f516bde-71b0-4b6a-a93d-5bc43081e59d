import {
  Column,
  CreateDateColumn,
  Entity,
  ManyToOne,
  PrimaryGeneratedColumn,
} from 'typeorm';
import { Interactions } from './interactions.entity';

@Entity()
export class InteractionResponseTime {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({
    type: 'decimal',
    precision: 5,
    scale: 2,
    comment: 'Insert time in miliseconds',
  })
  time: number;

  @ManyToOne(
    () => Interactions,
    (interactions) => interactions.interactionsResponseTime,
  )
  interactions: Interactions;

  @CreateDateColumn()
  createdAt: Date;
}
