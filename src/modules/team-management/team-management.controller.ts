import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Put,
  Req,
  UseGuards,
} from '@nestjs/common';

import { TeamManagementService } from './team-management.service';
import {
  CreateTeamDto,
  UpdateTeamDto,
  AddEmployeeDto,
  DeleteEmployeeDto,
} from './team-management.dto';
import { AuthGuard } from '../auth/auth.guard';

@UseGuards(AuthGuard)
@Controller('team-management')
export class TeamManagementController {
  constructor(private teamsService: TeamManagementService) {}

  @Get()
  async getAllTeam(@Req() req) {
    return this.teamsService.findAllTeam(req?.user?.company?.id);
  }

  @Get('/:id')
  getOneTeam(@Param('id') id: number) {
    return this.teamsService.findOneTeam(id);
  }

  @Post()
  createTeam(@Body() createTeamDto: CreateTeamDto, @Req() req) {
    return this.teamsService.createTeam(req?.user?.company?.id, createTeamDto);
  }

  @Put(':id')
  updateTeam(@Param('id') id: number, @Body() updateTeamDto: UpdateTeamDto) {
    return this.teamsService.updateTeam(id, updateTeamDto);
  }

  @Delete(':id')
  deleteTeam(@Param('id') id: number) {
    return this.teamsService.deleteTeams(id);
  }

  @Post('add-employee')
  addEmployeeToTeam(@Body() addEmployeeDto: AddEmployeeDto) {
    return this.teamsService.addEmployee(
      addEmployeeDto.employeeId,
      addEmployeeDto.teamId,
    );
  }

  @Delete('delete-employee')
  deleteEmployeeFromTeam(@Body() deleteEmployeeDto: DeleteEmployeeDto) {
    return this.teamsService.deleteEmployee(
      deleteEmployeeDto.employeeId,
      deleteEmployeeDto.teamId,
    );
  }

  @Post('default-team/:id')
  DefaultTeam(@Param('id') id: number, @Req() req) {
    return this.teamsService.makeTeamDefault(id, req?.company?.id);
  }
}
