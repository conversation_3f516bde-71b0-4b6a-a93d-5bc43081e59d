import {
  Column,
  CreateDateColumn,
  Entity,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';

import { Employee } from '../employee/employee.entity';
import { Company } from '../companies/companies.entity';
import { ChatSession } from '../chat-session/chat-session.entity';

@Entity()
export class Teams {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  name: string;

  @Column({ default: false })
  isDefault: boolean;

  @Column({ default: false })
  isSales: boolean;

  @ManyToOne(() => Company, (company) => company.teams)
  company: Company;

  @OneToMany(() => Employee, (employee) => employee.team)
  employees: Employee[];

  @OneToMany(() => ChatSession, (chatSession) => chatSession.team)
  chatSession: ChatSession[];

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
