import { Injectable, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';

import { Teams } from './team-management.entity';
import { CreateTeamDto, UpdateTeamDto } from './team-management.dto';
import { EmployeeService } from '../employee/employee.service';
import { CompaniesService } from '../companies/companies.service';
import { EventEmitter2, OnEvent } from '@nestjs/event-emitter';

@Injectable()
export class TeamManagementService {
  constructor(
    @InjectRepository(Teams)
    private readonly teamsRepository: Repository<Teams>,
    private employeeService: EmployeeService,
    private companyService: CompaniesService,
    private eventEmitter: EventEmitter2,
  ) {}

  async findAllTeam(companyId: number) {
    const company = await this.companyService.findOne({
      where: { id: companyId },
    });

    if (!company) {
      throw new BadRequestException('company not found');
    }

    return this.teamsRepository.find({
      where: {
        company: {
          id: company.id,
        },
      },
      relations: ['employees'],
      order: { createdAt: 'ASC' },
    });
  }

  async findOneTeam(id: number) {
    return this.teamsRepository.findOne({
      where: { id: id },
      relations: ['employees', 'company'],
    });
  }

  async createTeam(companyId: number, createTeamDto: CreateTeamDto) {
    const team = new Teams();

    const company = await this.companyService.getCompanyByCompanyId(companyId);

    if (!company) {
      throw new BadRequestException('company not found');
    }

    if (createTeamDto.isDefault === true) {
      await this.teamsRepository.update(
        { isDefault: true },
        { isDefault: false },
      );
      team.isDefault = true;
    } else if (createTeamDto.isSales === true) {
      await this.teamsRepository.update({ isSales: true }, { isSales: false });
      team.isSales = true;
    }

    team.name = createTeamDto.name;
    team.company = company;

    return this.teamsRepository.save(team);
  }

  async updateTeam(id: number, updateTeamDto: UpdateTeamDto) {
    const team = await this.findOneTeam(id);

    if (updateTeamDto.isDefault === true) {
      await this.teamsRepository.update(
        { isDefault: true },
        { isDefault: false },
      );

      team.isDefault = updateTeamDto.isDefault;
    } else if (updateTeamDto.isSales === true) {
      await this.teamsRepository.update({ isSales: true }, { isSales: false });
      team.isSales = updateTeamDto.isSales;
    }

    team.name = updateTeamDto.name;

    return this.teamsRepository.save(team);
  }

  async deleteTeams(id: number) {
    const team = await this.findOneTeam(id);
    return this.teamsRepository.remove(team);
  }

  async addEmployee(employeeId: number[], teamId: number) {
    const team = await this.findOneTeam(teamId);

    if (!team) {
      throw new BadRequestException('team not found');
    }

    for (const id of employeeId) {
      const employee = await this.employeeService.getOneEmployee(id);

      if (!employee) continue;

      team.employees.push(employee);
    }
    return this.teamsRepository.save(team);
  }

  async deleteEmployee(employeeId: number[], teamId: number) {
    const team = await this.findOneTeam(teamId);

    if (!team) {
      throw new BadRequestException('team not found');
    }
    for (const id of employeeId) {
      const index = team.employees.findIndex((obj) => obj.id === id);

      if (index === -1) continue;

      team.employees.splice(index, 1);
    }

    return this.teamsRepository.save(team);
  }

  async makeTeamDefault(id: number, companyId: number) {
    const teams = await this.findAllTeam(companyId);
    const haveDefaultTeam = teams.some((team) => team.isDefault === true);

    if (!haveDefaultTeam) {
      const team = await this.findOneTeam(id);
      team.isDefault = true;
      return this.teamsRepository.save(team);
    } else {
      throw new BadRequestException('there is a default team already');
    }
  }

  @OnEvent('company.created')
  async createDefaultTeam(payload: { companyId: number }) {
    const company = await this.companyService.getCompanyByCompanyId(
      payload.companyId,
    );

    const salesTeam = new Teams();
    const contactTeam = new Teams();

    salesTeam.name = 'sales team';
    salesTeam.isSales = true;
    salesTeam.isDefault = false;
    salesTeam.company = company;

    contactTeam.name = 'contact team';
    contactTeam.isSales = false;
    contactTeam.isDefault = true;
    contactTeam.company = company;

    await this.teamsRepository.save(salesTeam);
    await this.teamsRepository.save(contactTeam);

    return { salesTeam, contactTeam };
  }
}
