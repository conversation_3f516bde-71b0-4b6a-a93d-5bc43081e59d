import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

import { Teams } from './team-management.entity';
import { TeamManagementService } from './team-management.service';
import { TeamManagementController } from './team-management.controller';
import { EmployeeModule } from '../employee/employee.module';
import { CompaniesModule } from '../companies/companies.module';

@Module({
  imports: [TypeOrmModule.forFeature([Teams]), EmployeeModule, CompaniesModule],
  providers: [TeamManagementService],
  controllers: [TeamManagementController],
})
export class TeamManagementModule {}
