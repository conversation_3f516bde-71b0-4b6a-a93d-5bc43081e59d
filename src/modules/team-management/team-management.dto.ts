import { IsBoolean, IsOptional, IsString } from 'class-validator';

export class CreateTeamDto {
  @IsString()
  readonly name: string;

  @IsBoolean()
  @IsOptional()
  readonly isDefault: boolean;

  @IsBoolean()
  @IsOptional()
  readonly isSales: boolean;
}

export class UpdateTeamDto {
  @IsString()
  readonly name: string;

  @IsBoolean()
  @IsOptional()
  readonly isDefault: boolean;

  @IsBoolean()
  @IsOptional()
  readonly isSales: boolean;
}

export class AddEmployeeDto {
  readonly employeeId: number[];
  readonly teamId: number;
}

export class DeleteEmployeeDto {
  readonly employeeId: number[];
  readonly teamId: number;
}
