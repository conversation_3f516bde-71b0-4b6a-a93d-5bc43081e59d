import { Injectable } from '@nestjs/common';
import { ProductEntity } from './product.entity';
import { InjectRepository } from '@nestjs/typeorm';
import { In, Repository } from 'typeorm';
import { CreateProductDto } from './product.dto';
import { CompaniesService } from '../companies/companies.service';

@Injectable()
export class ProductService {
  constructor(
    @InjectRepository(ProductEntity)
    private productEntity: Repository<ProductEntity>,
    private companyService: CompaniesService,
  ) {}

  async getProducts(companyId: number) {
    return this.productEntity.find({ where: { company: { id: companyId } } });
  }

  async getProduct(productId: string) {
    return this.productEntity.findOne({ where: { id: productId } });
  }

  async getProductsByIds(productIds: string[]) {
    return this.productEntity.find({ where: { id: In(productIds) } });
  }

  async deleteProduct(productId: string) {
    return this.productEntity.delete({ id: productId });
  }

  async createProduct(userId: number, product: CreateProductDto) {
    const company = await this.companyService.getCompanyByEmployeeId(userId);

    return this.productEntity.save({ ...product, company });
  }
}
