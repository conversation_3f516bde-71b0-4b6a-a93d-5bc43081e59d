import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Put,
  Req,
  UseGuards,
} from '@nestjs/common';
import { CreateProductDto } from './product.dto';
import { ProductService } from './product.service';
import { AuthGuard } from '../auth/auth.guard';

@UseGuards(AuthGuard)
@Controller('product')
export class ProductController {
  constructor(private productService: ProductService) {}
  @Get()
  async getProducts(@Req() req) {
    return this.productService.getProducts(req.user.company.id);
  }

  @Get(':id')
  async getProduct(@Param('id') productId: string) {
    return this.productService.getProduct(productId);
  }

  @Put(':id')
  async updateProduct() {
    // return this.productService
  }

  @Delete(':id')
  async deleteProduct(@Param('id') productId: string) {
    return this.productService.deleteProduct(productId);
  }

  @Post()
  async createProduct(@Body() body: CreateProductDto, @Req() req) {
    return this.productService.createProduct(req.user.sub, body);
  }
}
