import { Injectable } from '@nestjs/common';
import { ChatSessionService } from '../chat-session/chat-session.service';
import { ChatResponseGeneratorService } from '../chat-response-generator/chat-response-generator.service';
import { JwtService } from '@nestjs/jwt';
import { TOKEN_PAYLOAD } from '../auth/auth.interface';
import { ChatSession } from '../chat-session/chat-session.entity';

@Injectable()
export class InboxService {
  constructor(
    private chatSessionService: ChatSessionService,
    private chatResponseGeneratorService: ChatResponseGeneratorService,
    private jwtService: JwtService,
  ) {}

  async getInboxByAuthToken(authToken: string): Promise<ChatSession[]> {
    const payload: TOKEN_PAYLOAD = await this.jwtService.verifyAsync(
      authToken,
      {
        secret: process.env.JWT_SECRET_KEY,
      },
    );

    const chatSessions =
      await this.chatSessionService.getChatSessionByEmployeeId(payload.sub);

    return chatSessions;
  }

  async markAsRead(messages: number[]) {
    for await (const message of messages) {
      this.chatResponseGeneratorService.markMessageAsViewed(message);
    }
  }
}
