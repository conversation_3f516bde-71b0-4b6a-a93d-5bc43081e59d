import { Logger } from '@nestjs/common';
import {
  ConnectedSocket,
  MessageBody,
  SubscribeMessage,
  WebSocketGateway,
  WebSocketServer,
} from '@nestjs/websockets';
import { Server, Socket } from 'socket.io';
import { INBOX_EVENT } from './inbox.enum';
import { AgentConnected } from './inbox.interface';
import { InboxService } from './inbox.service';
import { OnEvent } from '@nestjs/event-emitter';
import { RoomsService } from '../rooms/rooms.service';
import { AuthService } from '../auth/auth.service';
import { CompaniesService } from '../companies/companies.service';

@WebSocketGateway({ transports: ['websocket'] })
export class InboxGateway {
  @WebSocketServer()
  server: Server;

  constructor(
    private inboxesService: InboxService,
    private roomsService: RoomsService,
    private authService: AuthService,
    private companiesService: CompaniesService,
  ) {}

  afterInit(server: Server) {
    Logger.debug('Inbox socket Initialized');

    server.on('error', (error) => {
      Logger.error('Conexión WebSocket rechazada por CORS:', error);
    });
  }

  handleConnection(client: Socket, ...args: any[]) {
    Logger.debug(`user for inbox websocket connected: ${client.id}`);
  }

  @SubscribeMessage(INBOX_EVENT.AGENT_CONNECTED)
  async handleAgentConnected(
    @ConnectedSocket() client: Socket,
    @MessageBody() data: AgentConnected,
  ) {
    const inboxes = await this.inboxesService.getInboxByAuthToken(
      data.authToken,
    );

    const roomToJoin = new Array();
    for (const inbox of inboxes) {
      roomToJoin.push(
        this.roomsService.joinRoom(
          client,
          inbox.clientId,
          inbox.botCompany.company.id,
        ),
      );
    }

    await Promise.all(roomToJoin);

    client.emit(INBOX_EVENT.AGENT_CONNECTED, inboxes);

    Logger.debug('user connecting to inbox message');
  }

  @OnEvent('notification.message.new')
  async notificationMessageNew(payload) {
    const company = await this.companiesService.getCompanyBySessionId(
      payload.chatSession.clientId,
    );

    this.roomsService.emitToRoom(
      `${company.id}`,
      'notification.message.new',
      payload,
      this.server,
    );
  }

  @SubscribeMessage('notification.message.read')
  async notificationMessageRead(@MessageBody() data: { messagesId: number[] }) {
    await this.inboxesService.markAsRead(data.messagesId);
  }
}
