import { Modu<PERSON> } from '@nestjs/common';
import { InboxGateway } from './inbox.gateway';
import { InboxService } from './inbox.service';
import { ChatSessionModule } from '../chat-session/chat-session.module';
import { ChatResponseGeneratorModule } from '../chat-response-generator/chat-response-generator.module';
import { RoomsModule } from '../rooms/rooms.module';
import { AuthModule } from '../auth/auth.module';
import { CompaniesModule } from '../companies/companies.module';

@Module({
  imports: [
    ChatSessionModule,
    ChatResponseGeneratorModule,
    RoomsModule,
    AuthModule,
    CompaniesModule,
  ],
  providers: [InboxGateway, InboxService],
  exports: [InboxService],
})
export class InboxModule {}
