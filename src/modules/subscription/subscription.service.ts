import { BadRequestException, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Subscription } from './subscription.entity';
import { Repository } from 'typeorm';
import moment = require('moment');
import { CompaniesService } from '../companies/companies.service';

@Injectable()
export class SubscriptionService {
  constructor(
    @InjectRepository(Subscription)
    private subscriptionRepo: Repository<Subscription>,
    private companyService: CompaniesService,
  ) {}

  async getSubscriptionById(id: number) {
    return this.subscriptionRepo.findOne({ where: { id } });
  }

  async getSubscriptionByCompanyId(companyId: number) {
    return this.subscriptionRepo.findOne({
      where: { company: { id: companyId } },
    });
  }

  async createSubscription(companyId: number, subscriptionIdentifyId: string) {
    const company = await this.companyService.getCompanyByCompanyId(companyId);

    if (!company) {
      throw new BadRequestException('Company not exit');
    }

    const existSubscription = await this.subscriptionRepo.findOne({
      where: { company: { id: companyId } },
    });

    if (existSubscription) {
      existSubscription.idIdentify = subscriptionIdentifyId;
      existSubscription.company = company;

      await this.subscriptionRepo.save(existSubscription);
      return { subscribed: true };
    }

    const subscription = new Subscription();
    subscription.company = company;
    subscription.idIdentify = subscriptionIdentifyId;

    await this.subscriptionRepo.save(subscription);

    return { subscribed: true };
  }

  async cancelSubscription(subscriptionIdentifyId: string) {
    return this.subscriptionRepo.delete({
      idIdentify: subscriptionIdentifyId,
    });
  }
}
