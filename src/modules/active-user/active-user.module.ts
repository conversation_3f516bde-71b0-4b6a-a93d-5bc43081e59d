import { Module } from '@nestjs/common';
import { ActiveUserService } from './active-user.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ActiveUserEntity } from './active-user.entity';
import { ActiveUserController } from './active-user.controller';

@Module({
  imports: [TypeOrmModule.forFeature([ActiveUserEntity])],
  providers: [ActiveUserService],
  exports: [ActiveUserService],
  controllers: [ActiveUserController],
})
export class ActiveUserModule {}
