import { Controller, Get, Param, Query, UseGuards } from '@nestjs/common';
import { ActiveUserService } from './active-user.service';
import { GetActiveUserDto } from './active-user.dto';
import { AuthGuard } from '../auth/auth.guard';
import { ApiTags } from '@nestjs/swagger';

@ApiTags('get active user')
@UseGuards(AuthGuard)
@Controller('active-user')
export class ActiveUserController {
  constructor(private readonly activeUserService: ActiveUserService) {}

  @Get(':botRequestId')
  async getActiveUser(@Param() param: GetActiveUserDto) {
    return this.activeUserService.getActiveUsersByBotRequestId(
      param.botRequestId,
    );
  }
}
