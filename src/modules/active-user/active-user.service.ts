import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { ActiveUserEntity } from './active-user.entity';
import { Repository } from 'typeorm';

@Injectable()
export class ActiveUserService {
  constructor(
    @InjectRepository(ActiveUserEntity)
    private readonly activeUserRepository: Repository<ActiveUserEntity>,
  ) {}

  async createActiveUser(botRequestId: string, socketId: string) {
    const activeUser = new ActiveUserEntity();
    activeUser.botRequestId = botRequestId;
    activeUser.socketId = socketId;
    activeUser.isActive = true;

    return this.activeUserRepository.save(activeUser);
  }

  async desactiveUser(socketId: string) {
    const activeUser = await this.activeUserRepository.findOne({
      where: { socketId, isActive: true },
    });

    if (!activeUser) return;

    activeUser.isActive = false;

    return this.activeUserRepository.save(activeUser);
  }

  async getActiveUsersByBotRequestId(
    botRequestId: string,
  ): Promise<ActiveUserEntity[]> {
    const users = await this.activeUserRepository.find({
      where: { botRequestId, isActive: true },
    });

    if (!users) return [];

    return users;
  }

  async getAllActiveUserByBotRequestId(botRequestId: string) {
    return this.activeUserRepository.find({ where: { botRequestId } });
  }

  async getActiveUserBySocketId(socketId: string): Promise<ActiveUserEntity> {
    return this.activeUserRepository.findOne({ where: { socketId } });
  }
}
