export type messageStatus =
  | MessageStatus.PENDING
  | MessageStatus.SENT
  | MessageStatus.INVALID
  | MessageStatus.ACCEPTED
  | MessageStatus.REJECTED;

export enum MessageStatus {
  PENDING = 'pending',
  SENT = 'sent',
  INVALID = 'invalid',
  ACCEPTED = 'accepted',
  REJECTED = 'rejected',
}

export enum messageRole {
  SYSTEM = 'system',
  USER = 'user',
  ASSISTANT = 'assistant',
  AGENT = 'agent',
  TOOL = 'tool',
  FUNCTION = 'function',
  DEVELOPER = 'developer',
}

export interface MessageEventParams {
  messageType: messageStatus;
  phoneNumber: string;
}
