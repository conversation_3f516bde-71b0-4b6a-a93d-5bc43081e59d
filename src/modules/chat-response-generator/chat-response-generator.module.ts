import { Module, forwardRef } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

import { ChatResponseGeneratorService } from './chat-response-generator.service';
import { ChatResponseGeneratorController } from './chat-response-generator.controller';
import { OpenAiModule } from '../open-ai/open-ai.module';
import { ChatResponseGenerator } from './chat-response-generator.entity';
import { BotCompany } from '../bot-company/bot-company.entity';
import { LeadCenterModule } from '../lead-center/lead-center.module';
import { ContactModule } from '../contact/contact.module';
import { ChatSessionModule } from '../chat-session/chat-session.module';
import { CalendarsModule } from '../calendars/calendars.module';
import { OrderModule } from '../order/order.module';
import { ProductModule } from '../product/product.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([ChatResponseGenerator, BotCompany]),
    forwardRef(() => OpenAiModule),
    forwardRef(() => LeadCenterModule),
    forwardRef(() => ContactModule),
    forwardRef(() => ChatSessionModule),
    forwardRef(() => CalendarsModule),
    forwardRef(() => OrderModule),
    forwardRef(() => ProductModule),
  ],
  providers: [ChatResponseGeneratorService],
  controllers: [ChatResponseGeneratorController],
  exports: [ChatResponseGeneratorService],
})
export class ChatResponseGeneratorModule {}
