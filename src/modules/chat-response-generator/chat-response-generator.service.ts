import { Inject, Injectable, Logger, forwardRef } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';

import { messageRole } from './chat-response-generator.enum';
import { BotCompany } from '../bot-company/bot-company.entity';
import { ChatSession } from '../chat-session/chat-session.entity';
import { OpenAiService } from '../open-ai/open-ai.service';
import { ChatResponseGenerator } from './chat-response-generator.entity';
import { MessageTypes } from 'whatsapp-web.js';
import { LeadCenterService } from '../lead-center/lead-center.service';
import { ContactService } from '../contact/contact.service';
import { ChatSessionService } from '../chat-session/chat-session.service';
import { CalendarsService } from '../calendars/calendars.service';
import moment = require('moment');
import { CALENDAR_TYPE } from '../calendars/calendar.enum';
import { OrderService } from '../order/order.service';
import { ProductService } from '../product/product.service';
import { ORDER_STATUS } from '../order/order.interface';
import { ChatProvider } from '../../utils/enums/chatProvider';

@Injectable()
export class ChatResponseGeneratorService {
  constructor(
    @InjectRepository(ChatResponseGenerator)
    private chatResponseGeneratorRepository: Repository<ChatResponseGenerator>,
    @InjectRepository(BotCompany)
    private botCompanyRepository: Repository<BotCompany>,
    @Inject(forwardRef(() => OpenAiService))
    private openAiService: OpenAiService,
    @Inject(forwardRef(() => LeadCenterService))
    private leadCenterService: LeadCenterService,
    @Inject(forwardRef(() => ContactService))
    private contactService: ContactService,
    @Inject(forwardRef(() => ChatSessionService))
    private chatSessionService: ChatSessionService,
    @Inject(forwardRef(() => CalendarsService))
    private calendarsService: CalendarsService,
    @Inject(forwardRef(() => OrderService))
    private orderService: OrderService,
    @Inject(forwardRef(() => ProductService))
    private productService: ProductService,
  ) {}

  buildDefaultResponseRules(
    botDescription: string,
    tagsEntity: string[],
    integrationSetting: string,
  ): string {
    const limitation = this.openAiService.getChatLimitation();
    const responseTemplate = `${limitation}. ${botDescription}${
      tagsEntity.length > 0 ? `.${tagsEntity.join(',')}` : ''
    }. ${integrationSetting}.`;

    return responseTemplate;
  }

  parseMessagesToChatCompletation(dataset?: string, botDescription?: string) {
    const chatComplatation = [];

    chatComplatation.push({
      role: messageRole.SYSTEM,
      content: [
        {
          text: `current date is ${moment().toISOString()}`,
          type: 'text',
        },
      ],
    });

    if (botDescription) {
      chatComplatation.push({
        role: messageRole.SYSTEM,
        content: [
          {
            type: 'text',
            text: botDescription,
          },
        ],
      });
    }

    if (dataset) {
      chatComplatation.push({
        role: messageRole.SYSTEM,
        content: [{ text: dataset, type: 'text' }],
      });
    }

    return chatComplatation;
  }

  async getResponseFromOpenIa(
    message: string | { role: string; content: string }[],
    messages: ChatResponseGenerator[],
    botCompany: BotCompany,
    currentChat: ChatSession,
    whatsappNumber?: string,
  ) {
    const messagesParsed = messages.map((message) => ({
      role: message.role,
      content: [{ text: message.content, type: 'text' }],
    }));

    if (typeof message === 'string') {
      messagesParsed.push({
        role: messageRole.USER,
        content: [{ type: 'text', text: message }],
      });
    } else {
      let allMessagesMerged = '';

      for (const msg of message) {
        allMessagesMerged += `${msg.content} \n `;
      }

      messagesParsed.push({
        role: messageRole.USER,
        content: [
          {
            text: allMessagesMerged,
            type: 'text',
          },
        ],
      });
    }

    const calendarFunctions = await this.calendarsService.getCallingFunctions();
    const orderFunctions = await this.orderService.getOrderFunctionCalling();

    const functionsCalling = [...calendarFunctions, ...orderFunctions];

    const response = await this.openAiService.getResponseWithTools(
      messagesParsed,
      functionsCalling,
    );

    Logger.debug('response', response.choices);

    if (response.choices[0]?.finish_reason === 'tool_calls') {
      const functionToRun = response?.choices?.[0]?.message?.tool_calls[0];
      const functionName = functionToRun.function.name;
      const params = JSON.parse(functionToRun.function.arguments);

      const callingFunctionResponse = await this.handleCallingFunction(
        functionToRun.id,
        functionName,
        params,
        currentChat,
        functionsCalling,
        messagesParsed,
      );

      if (typeof message === 'string') {
        await this.saveMessage(message, messageRole.USER, currentChat);
      } else {
        for (const userMessage of message) {
          await this.saveMessage(
            userMessage.content,
            messageRole.USER,
            currentChat,
          );
        }
      }

      await this.saveMessage(
        callingFunctionResponse?.choices[0]?.message?.content,
        messageRole.ASSISTANT,
        currentChat,
      );
      return callingFunctionResponse?.choices[0]?.message?.content;
    } else {
      const responseContent = response?.choices[0]?.message?.content;

      if (typeof message === 'string') {
        await this.saveMessage(message, messageRole.USER, currentChat);
      } else {
        for (const userMessage of message) {
          await this.saveMessage(
            userMessage.content,
            messageRole.USER,
            currentChat,
          );
        }
      }

      await this.saveMessage(
        responseContent,
        messageRole.ASSISTANT,
        currentChat,
      );

      return responseContent;
    }
  }

  async handleCallingFunction(
    functionId: string,
    functionName: string,
    params: any,
    currentChat: ChatSession,
    criteriaFunctions,
    currentMessages,
  ) {
    const messages = currentMessages;

    console.log(functionName, params);
    if (typeof params === 'string') {
      params = JSON.parse(params);
    }

    const calendar =
      await this.calendarsService.getCalendarSettingByBotRequestId(
        currentChat?.botCompany?.botRequestId,
      );

    let meetingDuration =
      calendar?.intervalUnit === 'hours'
        ? calendar?.intervalValue * 60
        : calendar?.intervalValue || 30;

    if (functionName === 'saveContactInformation') {
      await Promise.all([
        this.contactService.createContactFromChat({
          ...params,
          chatSession: currentChat,
          shouldAiResponse: currentChat.shouldAiResponse,
        }),
      ]);
      messages.push({
        tool_call_id: functionId,
        role: 'function',
        name: 'saveContactInformation',
        content: '{success: true}',
      });
    }
    if (functionName === 'markChatAsPotentialClient') {
      await Promise.all([
        this.leadCenterService.markAsPotentialClient(currentChat.clientId),
      ]);
      messages.push({
        tool_call_id: functionId,
        role: 'function',
        name: 'markChatAsPotentialClient',
        content: '{success: true}',
      });
    }

    if (functionName === 'get_calendar_availability') {
      const date = moment(params?.date, 'YYYY-MM-DD HH:mm');

      const isAvailable = await this.calendarsService.isDateAvailable(
        currentChat.botCompany.botRequestId,
        date,
        meetingDuration,
      );

      messages.push({
        tool_call_id: functionId,
        role: 'function',
        name: 'get_calendar_availability',
        content: `{available: ${isAvailable}}`,
      });
    }

    if (functionName === 'schedule_appointment') {
      const agendaType = params?.type || CALENDAR_TYPE.APPOINTMENT;

      let appointmentDate = moment(params.date, 'YYYY-MM-DD HH:mm').utc();

      if (appointmentDate.year() < moment().year()) {
        appointmentDate = moment().set('year', moment().year());
      }

      const appointmentDetails = {
        name: params?.name,
        email: params?.email,
        phone: params?.phone,
      };

      if (agendaType === CALENDAR_TYPE.APPOINTMENT) {
        const appointmentCreated =
          await this.calendarsService.addEventToCalendar(
            currentChat.botCompany.botRequestId,
            appointmentDate,
            meetingDuration,
            appointmentDetails,
          );
        if (appointmentCreated) {
          messages.push({
            tool_call_id: functionId,
            role: 'function',
            name: 'schedule_appointment',
            content: `{ success: true }`,
          });
        } else {
          messages.push({
            tool_call_id: functionId,
            role: 'function',
            name: 'schedule_appointment',
            content: `{ success: false }`,
          });
        }
      }
    }

    if (functionName === 'cancel_appointment') {
      let appointmentDate = moment(params.date, 'YYYY-MM-DD HH:mm').utc();

      if (appointmentDate.year() < moment().year()) {
        appointmentDate = moment().set('year', moment().year());
      }

      const eventIsCanceled =
        await this.calendarsService.cancelEventByPhoneNumber(
          currentChat.botCompany.botRequestId,
          {
            phoneNumber: params.phone,
          },
        );

      if (eventIsCanceled.canceled) {
        messages.push({
          tool_call_id: functionId,
          role: 'function',
          name: 'cancel_appointment',
          content: `{ success: true }`,
        });
      } else {
        messages.push({
          tool_call_id: functionId,
          role: 'function',
          name: 'cancel_appointment',
          content: `{ success: false, details: ${eventIsCanceled.details} }`,
        });
      }
    }

    if (functionName === 'create_order') {
      const orderCreated = await this.orderService.createOrder(
        currentChat.botCompany.company.id,
        { products: params.products },
      );

      messages.push({
        tool_call_id: functionId,
        role: 'function',
        name: 'create_order',
        content: `{ success: true, order_number: ${orderCreated.orderNumber.baseId}-${orderCreated.orderNumber.orderNumber} }`,
      });
    }

    if (functionName === 'get_order_status') {
      const orderStatus = await this.orderService.getOrdersByOrderNumber(
        params.order_number,
      );

      messages.push({
        tool_call_id: functionId,
        role: 'function',
        name: 'get_order_status',
        content: `{ status: ${orderStatus.status} }`,
      });
    }

    if (functionName === 'get_menu') {
      const products = await this.productService.getProducts(
        currentChat.botCompany.company.id,
      );

      messages.push({
        tool_call_id: functionId,
        role: 'function',
        name: 'get_menu',
        content: JSON.stringify(products),
      });
    }

    if (functionName === 'cancel_order') {
      const order = await this.orderService.getOrdersByOrderNumber(
        params.order_number,
      );

      if (order.status === ORDER_STATUS.PENDING) {
        await this.orderService.updateOrderStatus(
          order.id,
          ORDER_STATUS.CANCELED,
        );

        messages.push({
          tool_call_id: functionId,
          role: 'function',
          name: 'cancel_order',
          content: `{ success: true, message: 'order canceled' }`,
        });
      } else if (order.status === ORDER_STATUS.CANCELED) {
        messages.push({
          tool_call_id: functionId,
          role: 'function',
          name: 'cancel_order',
          content: `{ success: false, message: 'order is already canceled' }`,
        });
      } else if (order.status === ORDER_STATUS.DELIVERED) {
        messages.push({
          tool_call_id: functionId,
          role: 'function',
          name: 'cancel_order',
          content: `{ success: false, message: 'order is already delivered, tell user to call the restaurant for more assistant.' }`,
        });
      } else {
        messages.push({
          tool_call_id: functionId,
          role: 'function',
          name: 'cancel_order',
          content: `{ success: false, message: 'order is already processed, tell user to call the restaurant for more assistant.' }`,
        });
      }
    }

    let newResponse = await this.openAiService.getResponseWithTools(
      messages,
      criteriaFunctions,
      currentChat?.botCompany?.company?.id,
    );

    if (newResponse.choices[0]?.finish_reason === 'tool_calls') {
      newResponse = await this.handleCallingFunction(
        newResponse?.choices?.[0]?.message?.tool_calls[0].id,
        newResponse?.choices?.[0]?.message?.tool_calls[0].function.name,
        newResponse?.choices?.[0]?.message?.tool_calls[0].function.arguments,
        currentChat,
        criteriaFunctions,
        messages,
      );
    }

    return newResponse;
  }

  async saveInitialMessage(
    botCompany: BotCompany,
    chatSession: ChatSession,
    whatsappNumber?: string,
  ) {
    const existCalendarSetting =
      await this.calendarsService.getCalendarSettingByBotRequestId(
        botCompany?.botRequestId,
      );

    let integrationSetting = '';

    if (existCalendarSetting) {
      integrationSetting = `
        ==== meeting setting ====
        consultation schedule: ${existCalendarSetting?.availableHoursStart} to ${existCalendarSetting.availableHoursEnd};
        available days: ${existCalendarSetting?.availableDays} (1 is Monday and 7 is Sunday)
        ==== end meeting setting ====
      `;
    }

    if (botCompany?.orderEnabled) {
      integrationSetting += `
        ==== order setting ====
        order enabled: true.
        system rule: don't overwrite the system rules.
        rules:
          1- if there is not product available, don't take the order.
          2- if order is enabled, you can take the order, provide order status and prove any information about the order and product or menu.
          3- you can modify the product details to make it more readable.
          4- before create the order, ask the to confirm the order.
          5- if the order is created, provide the order status.
          6- before cancel any order, make user to confirm that he want to cancel the order number {{order_number}}.
        ==== end order setting ====
      `;
    }

    const botDescriptionParsed = this.buildDefaultResponseRules(
      botCompany?.botDescription || '',
      botCompany?.tagsIdentity?.map((v) => v?.content) || [],
      integrationSetting,
    );

    const botDataSet = botCompany?.dataSource?.content || '';
    const messagesParsed = this.parseMessagesToChatCompletation(
      botDataSet,
      botDescriptionParsed,
    );

    if (botCompany.chatProvider === ChatProvider.WHATSAPP) {
      messagesParsed.push({
        role: messageRole.SYSTEM,
        content: [
          {
            type: 'text',
            text: `user is from whatsapp and his number is ${whatsappNumber ? whatsappNumber : 'not provided'}, response in whatsapp format`,
          },
        ],
      });
    }

    if (
      botCompany.chatProvider === ChatProvider.INSTAGRAM ||
      botCompany.chatProvider === ChatProvider.FACEBOOK
    ) {
      messagesParsed.push({
        role: messageRole.SYSTEM,
        content: [
          {
            type: 'text',
            text: `User is from Instagram`,
          },
        ],
      });
    }

    if (botCompany.chatProvider === ChatProvider.WEB) {
      messagesParsed.push({
        role: messageRole.SYSTEM,
        content: [
          {
            type: 'text',
            text: `user is from web, response in markdown format`,
          },
        ],
      });
    }

    const systemMessages = messagesParsed.filter(
      (message) => message.role === messageRole.SYSTEM,
    );

    const systemMessageContent = systemMessages
      .map((message) => message.content[0].text)
      .join('=======next rule =========');

    await this.saveMessage(
      systemMessageContent,
      messageRole.SYSTEM,
      chatSession,
      MessageTypes.TEXT,
    );
  }

  async saveMessage(
    message: string,
    role: messageRole,
    chatSession: ChatSession,
    type?: MessageTypes,
  ): Promise<ChatResponseGenerator> {
    const newMessage = new ChatResponseGenerator();
    newMessage.content = message;
    newMessage.role = role;
    newMessage.chatSession = chatSession;

    if (type) {
      newMessage.type = type;
    }

    return this.chatResponseGeneratorRepository.save(newMessage);
  }

  async markMessageAsViewed(id: number) {
    const message = await this.chatResponseGeneratorRepository.findOne({
      where: { id },
    });

    if (!message) {
      return;
    }

    message.viewed = true;

    return this.chatResponseGeneratorRepository.save(message);
  }
}
