import {
  <PERSON><PERSON><PERSON>,
  Column,
  PrimaryGeneratedColumn,
  ManyToOne,
  CreateDateColumn,
  UpdateDateColumn,
} from 'typeorm';
import { MessageTypes } from 'whatsapp-web.js';

import { ChatSession } from '../chat-session/chat-session.entity';
import { messageRole } from './chat-response-generator.enum';

@Entity()
export class ChatResponseGenerator {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ type: 'text' })
  role: messageRole;

  @Column({ type: 'text', nullable: true })
  content: string;

  @Column({ enum: MessageTypes, default: MessageTypes.TEXT })
  type: MessageTypes;

  @ManyToOne(
    () => ChatSession,
    (chatSession) => chatSession.chatResponseGenerator,
    { onDelete: 'CASCADE' },
  )
  chatSession: ChatSession;

  @Column({ type: 'boolean', default: false })
  viewed: boolean;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
