import {
  BadGatewayException,
  ForbiddenException,
  Inject,
  Injectable,
  forwardRef,
} from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import sizeOf from 'image-size';

import { Employee } from '../employee/employee.entity';
import { SecretKeyService } from '../secret-key/secret-key.service';
import { TagsIdentity } from '../tags-identity/tags-identity.entity';
import {
  CreateBotDto,
  ProviderDto,
  UpdateBotWebSetting,
} from './bot-company.dto';
import { BotCompany } from './bot-company.entity';
import { DigitalOceanService } from '../digital-ocean/digital-ocean.service';
import { DataSources } from '../data-sources/data-sources.entity';
import { ChatProvider } from '../../utils/enums/chatProvider';
import { WhatsappService } from '../whatsapp/whatsapp.service';

@Injectable()
export class BotCompanyService {
  constructor(
    @InjectRepository(BotCompany)
    private readonly botCompanyRepository: Repository<BotCompany>,
    @InjectRepository(TagsIdentity)
    private readonly tagsIdentityRepository: Repository<TagsIdentity>,
    @InjectRepository(Employee)
    private readonly employeeRepository: Repository<Employee>,
    @InjectRepository(DataSources)
    private readonly dataSourcesRepository: Repository<DataSources>,
    @Inject(forwardRef(() => SecretKeyService))
    private readonly secretKeyService: SecretKeyService,
    private readonly jwtService: JwtService,
    @Inject(forwardRef(() => DigitalOceanService))
    private readonly digitalOceanService: DigitalOceanService,
    @Inject(forwardRef(() => WhatsappService))
    private readonly whatsappService: WhatsappService,
  ) {}

  async getBotCompanyHistoryByBotRequestId(botRequestId: string) {
    const companyBot = await this.botCompanyRepository.findOne({
      where: { botRequestId },
      relations: ['bots'],
    });
    return companyBot;
  }

  async getBotCompany(employeeId: number, type?: ChatProvider) {
    const botCompany = await this.botCompanyRepository.find({
      where: { company: { employees: { id: employeeId } }, isDeleted: false },
      relations: [
        'company',
        'tagsIdentity',
        'dataSource',
        'whatsappConfig',
        'selectedSocialAuth',
      ],
    });

    if (!botCompany) {
      return { botCompany: [] };
    }

    return {
      botCompany,
    };
  }

  async updateBotCompanyChatIcon(
    botRequestId: string,
    file: Express.Multer.File,
  ): Promise<string> {
    const imageType = ['image/png', 'image/jpg', 'image/jpeg', 'image/svg'];
    const imageSize = 1000000;
    const imageDimension = { width: 96, height: 96 };

    if (!imageType.includes(file.mimetype)) {
      throw new ForbiddenException('Invalid file type must be image');
    }

    if (file.size > imageSize) {
      throw new ForbiddenException('Invalid file size must be less than 1MB');
    }

    const imageDimensions = sizeOf(file.buffer);
    if (
      imageDimensions.width > imageDimension.width ||
      imageDimensions.height > imageDimension.height
    ) {
      throw new ForbiddenException(
        'Invalid file dimension, must be less than 96x96',
      );
    }

    const botCompany = await this.botCompanyRepository.findOne({
      where: { botRequestId },
      relations: ['company'],
    });

    if (!botCompany) {
      throw new BadGatewayException('Company not found');
    }

    try {
      botCompany.chatIconUrl = file.buffer;
      await this.botCompanyRepository.save(botCompany);

      return;
    } catch (error) {
      console.log(error);
      throw new BadGatewayException('Error uploading file');
    }
  }

  async toggleOrder(botRequestId: string, orderEnabled: boolean) {
    const botCompany = await this.botCompanyRepository.findOne({
      where: { botRequestId },
    });

    if (!botCompany) {
      throw new BadGatewayException('Bot not found');
    }

    botCompany.orderEnabled = orderEnabled;
    await this.botCompanyRepository.save(botCompany);

    return botCompany;
  }

  async createBotCompany(token: string, botCompany: CreateBotDto) {
    const decodeToken = this.jwtService.decode(token);
    const employeeId = decodeToken.sub;

    const employee = await this.employeeRepository.findOne({
      where: { id: employeeId },
      relations: ['company'],
    });

    if (!employee) {
      throw new BadGatewayException('Company not found');
    }

    const botRequestId = await this.createBotRequestId();

    const tagsIdentity = await this.tagsIdentityRepository.find({
      where: botCompany?.identity?.tagsIdentity?.map((identity) => ({
        id: identity.id,
      })),
    });

    const dataSource = await this.dataSourcesRepository.findOne({
      where: { id: botCompany.dataSource.id },
    });

    const botCompanyToSave = new BotCompany();

    if (botCompany.providerConfig.provider === ChatProvider.WEB) {
      botCompanyToSave.primaryColor =
        botCompany.configuration.userMessageColorBackground;
      botCompanyToSave.primaryColorText =
        botCompany.configuration.userMessageColorText;
      botCompanyToSave.showWatermark = botCompany.configuration.showWatermark;
      botCompanyToSave.chatOpenIconBackgroundColor =
        botCompany.configuration.chatOpenIconBackgroundColor;
      botCompanyToSave.chatOpenIconTextColor =
        botCompany.configuration.chatOpenIconTextColor;
      botCompanyToSave.botInitialMessage =
        botCompany.configuration.botInitialMessage;
    }

    botCompanyToSave.company = employee.company;
    botCompanyToSave.botName = botCompany.identity.botName;
    botCompanyToSave.botDescription = botCompany.identity.botDescription;
    botCompanyToSave.botRequestId = botRequestId;
    botCompanyToSave.tagsIdentity = tagsIdentity;
    botCompanyToSave.quickReplies = [];
    botCompanyToSave.allowedDomain = botCompany.identity.allowedDomain;
    botCompanyToSave.isBotEnabled = botCompany.identity.isBotEnabled;
    botCompanyToSave.dataSource = dataSource;
    botCompanyToSave.chatProvider = botCompany.providerConfig.provider;

    const newBotCompany =
      await this.botCompanyRepository.save(botCompanyToSave);

    if (botCompany.providerConfig.provider === ChatProvider.WHATSAPP) {
      await this.whatsappService.createWhatsappConfig(newBotCompany);
    }

    return { ...newBotCompany, botRequestId };
  }

  async createBotRequestId() {
    return this.secretKeyService.createBotRequestId();
  }

  async getAllBotCompany(companyId: number) {
    return this.botCompanyRepository.find({
      where: { company: { id: companyId } },
    });
  }

  async getBotCompanyByBotRequestId(botRequestId: string): Promise<BotCompany> {
    return this.botCompanyRepository.findOne({
      where: { botRequestId, isDeleted: false },
      relations: ['company'],
    });
  }

  async updateBotSettings(botRequestId: string, botInfo: UpdateBotWebSetting) {
    const botCompany = await this.getBotCompanyByBotRequestId(botRequestId);

    if (!botCompany) {
      throw new BadGatewayException('Bot not found');
    }
    if (botInfo.configuration) {
      botCompany.botInitialMessage = botInfo.configuration.botInitialMessage;
      botCompany.primaryColor =
        botInfo.configuration.userMessageColorBackground;
      botCompany.primaryColorText = botInfo.configuration.userMessageColorText;
      botCompany.showWatermark = botInfo.configuration.showWatermark;
      botCompany.chatOpenIconBackgroundColor =
        botInfo.configuration.chatOpenIconBackgroundColor;
      botCompany.chatOpenIconTextColor =
        botInfo.configuration.chatOpenIconTextColor;
    }

    if (botInfo.identity) {
      const tagsIdentity = await this.tagsIdentityRepository.find({
        where: botInfo?.identity?.tagsIdentity.map((identity) => ({
          id: identity.id,
        })),
      });

      botCompany.botName = botInfo.identity.botName;
      botCompany.botDescription = botInfo.identity.botDescription;
      botCompany.allowedDomain = botInfo.identity.allowedDomain;
      botCompany.isBotEnabled = botInfo.identity.isBotEnabled;
      botCompany.tagsIdentity = tagsIdentity;
    }

    if (botInfo?.dataSource?.id) {
      const dataSource = await this.dataSourcesRepository.findOne({
        where: { id: botInfo.dataSource.id },
      });

      botCompany.dataSource = dataSource;
    }

    botCompany.quickReplies = [];
    await this.botCompanyRepository.save(botCompany);

    const botCompanySaved = await this.botCompanyRepository.findOne({
      where: { botRequestId },
    });

    return botCompanySaved;
  }

  async updateBotCompanyProviderByBotRequestId(
    botRequestId: string,
    providerConfig: ProviderDto,
  ) {
    const botCompany = await this.botCompanyRepository.findOne({
      where: { botRequestId },
    });

    if (!botCompany) {
      throw new BadGatewayException('Bot not found');
    }
  }

  async deleteBotCompany(id: number) {
    const botCompany = await this.botCompanyRepository.findOne({
      where: { id },
    });

    if (!botCompany) {
      throw new BadGatewayException('Bot not found');
    }

    await this.botCompanyRepository.update(
      { id },
      { isDeleted: true, isBotEnabled: false, deletedAt: new Date() },
    );
  }
}
