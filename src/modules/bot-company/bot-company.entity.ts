import {
  <PERSON><PERSON>ty,
  Column,
  PrimaryGeneratedColumn,
  OneToMany,
  ManyToOne,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToMany,
  JoinTable,
  JoinColumn,
  OneToOne,
} from 'typeorm';

import { Company } from '../companies/companies.entity';
import { SecretKey } from '../secret-key/secret-key.entity';
import { TagsIdentity } from '../tags-identity/tags-identity.entity';
import { ChatSession } from '../chat-session/chat-session.entity';
import { DataSources } from '../data-sources/data-sources.entity';
import { WhatsappConfig } from '../whatsapp/whatsapp-config.entity';
import { ChatProvider } from '../../utils/enums/chatProvider';
import { SocialAuth } from '../social-auth/social-auth.entity';
import { FacebookConfig } from '../facebook/facebook-config.entity';
import { SocialAuthAccount } from '../social-auth/social-auth-account.entity';

@Entity()
export class BotCompany {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ nullable: true })
  botName: string;

  @Column({ nullable: true, type: 'text' })
  botDescription: string;

  @ManyToOne(() => DataSources, (dataSources) => dataSources.botCompany, {
    eager: true,
  })
  @JoinColumn()
  dataSource: DataSources;

  @Column({ type: 'boolean', default: false })
  orderEnabled: boolean;

  @Column({ nullable: true, type: 'text' })
  botInitialMessage: string;

  @Column({ default: '#500d82' })
  primaryColor: string;

  @Column({ default: '#000000' })
  primaryColorText: string;

  @Column({ default: '#F2E3FD' })
  secondaryColor: string;

  @Column({ default: '#000000' })
  secondaryColorText: string;

  @Column({ nullable: true, type: 'simple-array' })
  quickReplies: string[];

  @ManyToOne(() => Company, (company) => company.botCompany)
  company: Company;

  @Column({ nullable: true })
  botDataSourceFileId: string;

  @Column({ nullable: false })
  botRequestId: string;

  @Column({ nullable: true })
  allowedDomain: string;

  @OneToMany(() => SecretKey, (secretKey) => secretKey.company, {
    nullable: false,
  })
  secretKeys: SecretKey[];

  @Column({ nullable: false, default: false })
  isDeleted: boolean;

  @Column({ nullable: false, default: false })
  isBotEnabled: boolean;

  @OneToMany(
    () => SocialAuthAccount,
    (socialAuthAccount) => socialAuthAccount.selectedBotCompany,
  )
  selectedSocialAuth: SocialAuth;

  @Column({ nullable: true, type: 'text' })
  botDataSourceFormatted: string;

  @OneToMany(() => ChatSession, (chatSession) => chatSession.botCompany, {
    onDelete: 'CASCADE',
  })
  chatSession: ChatSession[];

  @ManyToMany(() => TagsIdentity, (tagsIdentity) => tagsIdentity.botCompany, {
    eager: true,
  })
  @JoinTable()
  tagsIdentity: TagsIdentity[];

  @Column({ nullable: true, type: 'bytea' })
  chatIconUrl: Buffer;

  @Column({ nullable: true, type: 'text', default: '#000000' })
  chatOpenIconBackgroundColor: string;

  @Column({ nullable: true, type: 'text', default: '#ffffff' })
  chatOpenIconTextColor: string;

  @Column({ nullable: false, type: 'boolean', default: true })
  showWatermark: boolean;

  @OneToOne(
    () => WhatsappConfig,
    (whatsappConfig) => whatsappConfig.botCompany,
    { nullable: true, eager: true, onDelete: 'SET NULL' },
  )
  @JoinColumn()
  whatsappConfig: WhatsappConfig;

  @OneToOne(
    () => FacebookConfig,
    (facebookConfig) => facebookConfig.botCompany,
    { nullable: true, eager: true, onDelete: 'SET NULL' },
  )
  @JoinColumn()
  facebookConfig: FacebookConfig;

  @Column({ enum: ChatProvider, default: ChatProvider.WEB })
  chatProvider: ChatProvider;

  @Column({ type: 'timestamp', nullable: true })
  deletedAt: Date;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  constructor() {
    this.quickReplies = [];
  }
}
