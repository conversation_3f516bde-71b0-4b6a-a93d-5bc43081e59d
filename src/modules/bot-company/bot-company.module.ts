import { Module, forwardRef } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

import { BotCompany } from './bot-company.entity';
import { BotCompanyService } from './bot-company.service';
import { BotCompanyController } from './bot-company.controller';
import { Company } from '../companies/companies.entity';
import { SecretKeyModule } from '../secret-key/secret-key.module';
import { MessageEntryPoint } from '../message-entry-point/message-entry-point.entity';
import { Employee } from '../employee/employee.entity';
import { TagsIdentity } from '../tags-identity/tags-identity.entity';
import { DigitalOceanModule } from '../digital-ocean/digital-ocean.module';
import { DataSources } from '../data-sources/data-sources.entity';
import { WhatsappModule } from '../whatsapp/whatsapp.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      BotCompany,
      Company,
      MessageEntryPoint,
      Employee,
      TagsIdentity,
      DataSources,
    ]),
    forwardRef(() => SecretKeyModule),
    forwardRef(() => DigitalOceanModule),
    forwardRef(() => WhatsappModule),
  ],
  providers: [BotCompanyService],
  exports: [BotCompanyService],
  controllers: [BotCompanyController],
})
export class BotCompanyModule {}
