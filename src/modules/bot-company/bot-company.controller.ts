import {
  Body,
  Controller,
  Delete,
  Get,
  HttpCode,
  Param,
  Post,
  Put,
  Req,
  UploadedFile,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { ApiConsumes, ApiTags } from '@nestjs/swagger';
import { FileInterceptor } from '@nestjs/platform-express';

import { AuthGuard } from '../auth/auth.guard';
import {
  CreateBotDto,
  ProviderDto,
  ToggleOrderEnabledDto,
  UpdateBotWebIcon,
  UpdateBotWebSetting,
} from './bot-company.dto';
import { BotCompanyService } from './bot-company.service';
import { ChatProvider } from '../../utils/enums/chatProvider';

@ApiTags('Bot Company')
@UseGuards(AuthGuard)
@Controller('bot-company')
export class BotCompanyController {
  constructor(private botCompanyService: BotCompanyService) {}

  @Get()
  async getBotCompanies(@Req() req) {
    return this.botCompanyService.getBotCompany(req.user.sub);
  }

  @Post('create')
  async createBotCompany(@Body() body: CreateBotDto, @Req() req: any) {
    const token = req.headers.authorization.split(' ')[1];
    return this.botCompanyService.createBotCompany(token, body);
  }

  @Put(':botRequestId/order')
  async toggleOrder(
    @Body() body: ToggleOrderEnabledDto,
    @Param('botRequestId') botRequestId: string,
  ) {
    return this.botCompanyService.toggleOrder(botRequestId, body.orderEnabled);
  }

  @Put(':botRequestId/provider')
  async updateBotProvider(
    @Body() body: ProviderDto,
    @Param('botRequestId') botRequestId: string,
  ) {
    return this.botCompanyService.updateBotCompanyProviderByBotRequestId(
      botRequestId,
      body,
    );
  }

  @ApiConsumes('multipart/form-data')
  @UseInterceptors(FileInterceptor('file'))
  @Put('web/icon')
  async updateChatIcon(
    @Body() body: UpdateBotWebIcon,
    @UploadedFile() file: Express.Multer.File,
  ) {
    return this.botCompanyService.updateBotCompanyChatIcon(
      body.botRequestId,
      file,
    );
  }

  @Get('web')
  async getBotCompany(@Req() req) {
    return this.botCompanyService.getBotCompany(req.user.sub, ChatProvider.WEB);
  }

  @HttpCode(200)
  @Put('web/setting/:botRequestId')
  async updateBotCompany(
    @Body() body: UpdateBotWebSetting,
    @Param('botRequestId') botRequestId: string,
  ) {
    return this.botCompanyService.updateBotSettings(botRequestId, body);
  }

  @Delete(':id')
  async deleteBotCompany(@Param('id') id: number) {
    return this.botCompanyService.deleteBotCompany(id);
  }
}
