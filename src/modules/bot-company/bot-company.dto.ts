import { Transform } from 'class-transformer';
import {
  IsArray,
  IsBoolean,
  IsEnum,
  IsNumber,
  IsOptional,
  IsString,
  IsUrl,
  ValidateNested,
} from 'class-validator';
import { ChatProvider } from '../../utils/enums/chatProvider';

interface TagsIdentity {
  id: number;
  name: string;
  content: string;
}

export class ToggleOrderEnabledDto {
  @IsBoolean()
  readonly orderEnabled: boolean;
}

export class IdentityDto {
  @IsString()
  botName: string;

  @IsString()
  botDescription: string;

  @IsArray()
  @ValidateNested({ each: true })
  tagsIdentity: TagsIdentity[];

  @IsUrl()
  allowedDomain: string;

  @IsBoolean()
  isBotEnabled: boolean;
}

export class ConfigurationDto {
  @IsString()
  readonly botInitialMessage: string;

  @IsString()
  readonly userMessageColorBackground: string;

  @IsString()
  readonly userMessageColorText: string;

  @IsString()
  readonly chatOpenIconBackgroundColor: string;

  @IsString()
  readonly chatOpenIconTextColor: string;

  @IsBoolean()
  readonly showWatermark: boolean;
}

export class UpdateBotWebIcon {
  @IsString()
  readonly botRequestId: string;
}

export class DataSourcesDto {
  @IsNumber()
  @IsOptional()
  @Transform(({ value }) => Number(value))
  readonly id: number;
}

export class ProviderDto {
  @IsEnum(ChatProvider)
  readonly provider: ChatProvider;
}
export class CreateBotDto {
  readonly dataSource: DataSourcesDto;

  readonly identity: IdentityDto;

  readonly configuration: ConfigurationDto;

  readonly providerConfig: ProviderDto;
}

export class UpdateBotWebSetting extends CreateBotDto {}
