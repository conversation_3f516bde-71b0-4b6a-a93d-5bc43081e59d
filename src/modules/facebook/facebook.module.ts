import { forwardRef, <PERSON><PERSON><PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

import { FacebookController } from './facebook.controller';
import { FacebookService } from './facebook.service';
import { FacebookMessages } from './facebook.entity';
import { ChatSessionModule } from '../chat-session/chat-session.module';
import { SocialAuthModule } from '../social-auth/social-auth.module';
import { FacebookConfig } from './facebook-config.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([FacebookMessages, FacebookConfig]),
    forwardRef(() => SocialAuthModule),
    forwardRef(() => ChatSessionModule),
  ],
  controllers: [FacebookController],
  providers: [FacebookService],
  exports: [FacebookService],
})
export class FacebookModule {}
