import {
  forwardRef,
  HttpException,
  HttpStatus,
  Inject,
  Injectable,
  Logger,
} from '@nestjs/common';
import axios from 'axios';
import { FacebookMessageWebhook, FacebookSources } from './facebook.interface';
import { InjectRepository } from '@nestjs/typeorm';
import { FacebookMessages } from './facebook.entity';
import { Repository } from 'typeorm';
import { ChatSessionService } from '../chat-session/chat-session.service';
import { messageRole } from '../chat-response-generator/chat-response-generator.enum';
import { MessageTypes } from 'whatsapp-web.js';
import { SocialAuthService } from '../social-auth/social-auth.service';
import { FacebookConfig } from './facebook-config.entity';
import { BotCompany } from '../bot-company/bot-company.entity';
import { SocialAuthStatus } from '../social-auth/social-auth.interface';

@Injectable()
export class FacebookService {
  private readonly GRAPH_API_URL = 'https://graph.facebook.com/v20.0';

  constructor(
    @InjectRepository(FacebookMessages)
    private facebookMessagesRepo: Repository<FacebookMessages>,
    @InjectRepository(FacebookConfig)
    private facebookConfigRepo: Repository<FacebookConfig>,
    @Inject(forwardRef(() => SocialAuthService))
    private socialAuthService: SocialAuthService,
    @Inject(forwardRef(() => ChatSessionService))
    private chatSessionService: ChatSessionService,
  ) {}

  async createFacebookConfig(botCompany: BotCompany) {
    const newFacebookConfig = new FacebookConfig();
    newFacebookConfig.botCompany = botCompany;

    return this.facebookConfigRepo.save(newFacebookConfig);
  }

  async handleFacebookWebhook(data: FacebookMessageWebhook) {
    Logger.debug(data);

    const businessId = data?.entry[0]?.id;

    const socialAuthAccount =
      await this.socialAuthService.getSocialAuthAccountByAccountId(businessId);

    if (!socialAuthAccount) {
      Logger.debug(`social auth account not exist for ${businessId}`);
      return;
    }

    if (!socialAuthAccount) {
      Logger.debug(`social auth not exist for ${businessId} account`);
      return;
    }

    if (!socialAuthAccount.selectedBotCompany) {
      Logger.debug(
        `social auth account not exist for ${businessId} account`,
        socialAuthAccount,
      );
      throw new HttpException(
        'Social auth account not exist',
        HttpStatus.BAD_REQUEST,
      );
    }

    const isMessage = Object.keys(data?.entry[0]).some(
      (key) => key === 'messaging',
    );

    const messageText = data?.entry[0]?.messaging[0]?.message?.text;

    if (
      isMessage &&
      data?.entry[0]?.messaging[0]?.recipient.id === businessId &&
      messageText
    ) {
      const senderId = data?.entry[0]?.messaging[0]?.sender?.id;

      return this.handleFacebookMessage({
        botRequestId: socialAuthAccount?.selectedBotCompany?.botRequestId,
        messageText,
        messageTime: data?.entry[0]?.messaging[0]?.timestamp.toString(),
        senderId,
        recipientId: businessId,
      });
    }
  }

  async handleFacebookMessage({
    botRequestId,
    senderId,
    recipientId,
    messageText,
    messageTime,
  }: {
    botRequestId: string;
    senderId: string;
    recipientId: string;
    messageText: string;
    messageTime: string;
  }) {
    const socialAuthAccount =
      await this.socialAuthService.getSocialAuthAccountByAccountId(recipientId);

    if (
      !socialAuthAccount ||
      socialAuthAccount.status !== SocialAuthStatus.CONNECTED
    ) {
      Logger.debug(`social auth not exist for ${recipientId} account`);

      throw new HttpException(
        'Social auth account not connected',
        HttpStatus.BAD_REQUEST,
      );
    }

    const chatExist = await this.facebookMessagesRepo.findOne({
      where: { senderId },
    });

    const chatSession = await this.chatSessionService.initChatSession(
      botRequestId,
      false,
      chatExist?.chatSession?.clientId || '',
    );

    const response = await this.chatSessionService.handleMessage(
      messageText,
      chatSession.chat.botCompany,
      chatSession.sessionId,
      messageRole.USER,
      null,
      MessageTypes.TEXT,
    );

    if (!chatExist) {
      const recipientDetails = await this.getRecipientName(senderId);
      Logger.debug(recipientDetails);

      const message = new FacebookMessages();
      message.source = FacebookSources.INSTAGRAM;
      message.senderId = senderId;
      message.recipientId = recipientId;
      message.messageTime = messageTime;
      message.chatSession = chatSession.chat;
      message.recipientName = recipientDetails?.name || '';
      message.recipientUsername = recipientDetails?.username || '';

      await this.facebookMessagesRepo.save(message);
    }

    if (response) {
      return this.responseToInstagram({
        message: response,
        recipientId: senderId,
        token: socialAuthAccount.token,
      });
    }
  }

  async getFacebookAccessTokenByAccountId(accountId: string) {
    const socialAuth =
      await this.socialAuthService.getSocialAuthByAccountId(accountId);

    if (socialAuth) {
      return socialAuth.socialAuthAccount[0].token;
    }

    return null;
  }

  async responseToInstagram({
    message,
    recipientId,
    token,
  }: {
    message: string;
    recipientId: string;
    token: string;
  }) {
    try {
      const url = `${this.GRAPH_API_URL}/me/messages`;

      const response = await axios.post(
        url,
        {
          recipient: {
            id: recipientId,
          },
          messaging_type: 'RESPONSE',
          message: {
            text: message,
          },
        },
        {
          headers: { 'Content-Type': 'application/json' },
          params: { access_token: token },
        },
      );
      return response.data;
    } catch (error) {
      throw new HttpException(
        error.response?.data || 'Error enviando mensaje',
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  async getRecipientName(recipientId: string) {
    const url = `${this.GRAPH_API_URL}/${recipientId}?fields=id,username,name&access_token=${process.env.FACEBOOK_TOKEN}`;

    try {
      const response = await axios.get(url);

      return response.data;
    } catch (error) {
      Logger.debug(`username not found for ${recipientId}`);
      return null;
    }
  }
}
