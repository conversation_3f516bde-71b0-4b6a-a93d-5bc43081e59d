import {
  CreateDateColumn,
  Entity,
  JoinColumn,
  OneToMany,
  OneToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';

import { BotCompany } from '../bot-company/bot-company.entity';
import { FacebookMessages } from './facebook.entity';

@Entity()
export class FacebookConfig {
  @PrimaryGeneratedColumn()
  id: number;

  @OneToOne(() => BotCompany, (botCompany) => botCompany.facebookConfig)
  botCompany: BotCompany;

  @OneToMany(
    () => FacebookMessages,
    (facebookMessages) => facebookMessages.facebookConfig,
    {
      cascade: true,
      eager: true,
    },
  )
  @JoinColumn()
  chats: FacebookMessages[];

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
