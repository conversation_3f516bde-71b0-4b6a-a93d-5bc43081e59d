import { Body, Controller, Get, Post, Query } from '@nestjs/common';
import { FacebookService } from './facebook.service';
import { FacebookMessageWebhook } from './facebook.interface';

@Controller('facebook')
export class FacebookController {
  constructor(private facebookService: FacebookService) {}

  // use it to validate webhook url from developers.facebook.com
  @Get('webhook')
  async validateWebhookFromFacebook(@Query() param: any) {
    return param['hub.challenge'];
  }

  @Post('webhook')
  async handleWebhookMessages(@Body() body: FacebookMessageWebhook) {
    return this.facebookService.handleFacebookWebhook(body);
  }
}
