import {
  <PERSON><PERSON><PERSON>,
  CreateDate<PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  ManyToOne,
  OneToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { FacebookSources } from './facebook.interface';
import { ChatSession } from '../chat-session/chat-session.entity';
import { BotCompany } from '../bot-company/bot-company.entity';
import { FacebookConfig } from './facebook-config.entity';

@Entity()
export class FacebookMessages {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ enum: [FacebookSources] })
  source: FacebookSources;

  @Column()
  senderId: string;

  @Column({ unique: true })
  recipientId: string;

  @Column({ nullable: true })
  recipientName: string;

  @Column({ nullable: true })
  recipientUsername: string;

  @Column({ type: 'text' })
  messageTime: string;

  @OneToOne(() => ChatSession, (chatSession) => chatSession.facebookChat, {
    onDelete: 'CASCADE',
    eager: true,
  })
  @JoinColumn()
  chatSession: ChatSession;

  @ManyToOne(() => FacebookConfig, (facebookConfig) => facebookConfig.chats)
  facebookConfig: FacebookConfig;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
