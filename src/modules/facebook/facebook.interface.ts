export enum FacebookSources {
  FACEBOOK = 'facebook',
  INSTAGRAM = 'instagram',
}

export interface SenderRecipient {
  id: string;
}

export interface Message {
  mid: string;
  text: string;
}

export interface Messaging {
  sender: SenderRecipient;
  recipient: SenderRecipient;
  timestamp: number;
  message: Message;
}

export interface Entry {
  time: number;
  id: string;
  messaging: Messaging[];
}

export interface FacebookMessageWebhook {
  object: FacebookSources;
  entry: Entry[];
}
