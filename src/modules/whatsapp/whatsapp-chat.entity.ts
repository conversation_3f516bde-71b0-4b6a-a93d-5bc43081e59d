import {
  <PERSON>um<PERSON>,
  CreateDate<PERSON>olumn,
  <PERSON>tity,
  <PERSON>inC<PERSON>um<PERSON>,
  ManyToOne,
  OneToMany,
  OneToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { WhatsappConfig } from './whatsapp-config.entity';
import { ChatSession } from '../chat-session/chat-session.entity';
import { ChatId, MessageId } from 'whatsapp-web.js';

@Entity()
export class WhatsappChat {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  fromPhoneNumber: string;

  @Column({ nullable: true })
  chatId: string;

  @ManyToOne(() => WhatsappConfig, (whatsappConfig) => whatsappConfig.chats)
  whatsappConfig: WhatsappConfig;

  @OneToOne(() => ChatSession, (chatSession) => chatSession.whatsappChat, {
    onDelete: 'CASCADE',
  })
  @JoinColumn()
  chatSession: ChatSession;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
