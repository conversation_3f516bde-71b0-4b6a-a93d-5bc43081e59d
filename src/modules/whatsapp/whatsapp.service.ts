import { Inject, Injectable, forwardRef, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { rimraf } from 'rimraf';
import { EventEmitter2, OnEvent } from '@nestjs/event-emitter';
import * as whatsappWebJs from 'whatsapp-web.js';
import * as qrcode from 'qrcode-terminal';
import { existsSync, readdirSync } from 'fs';

import { BotCompany } from '../bot-company/bot-company.entity';
import { WhatsappConfig } from './whatsapp-config.entity';
import { DigitalOceanService } from '../digital-ocean/digital-ocean.service';
import { WhatsappAuthStatus, WhatsappEvents } from './whatsapp.enum';
import { ChatSessionService } from '../chat-session/chat-session.service';
import { messageRole } from '../chat-response-generator/chat-response-generator.enum';
import { WhatsappChat } from './whatsapp-chat.entity';
import { CompaniesService } from '../companies/companies.service';

@Injectable()
export class WhatsappService {
  private authPath = `${process.env.INIT_CWD}/.whatsapp-auth`;
  private runningClients: Map<string, whatsappWebJs.Client> = new Map<
    string,
    whatsappWebJs.Client
  >();
  private activeCompanies: Set<string> = new Set();

  constructor(
    @InjectRepository(BotCompany)
    private readonly botCompanyRepository: Repository<BotCompany>,
    @InjectRepository(WhatsappConfig)
    private readonly whatsappConfigRepository: Repository<WhatsappConfig>,
    @InjectRepository(WhatsappChat)
    private readonly whatsappChatRepository: Repository<WhatsappChat>,
    @Inject(forwardRef(() => DigitalOceanService))
    private readonly digitalOceanService: DigitalOceanService,
    private readonly eventEmitter: EventEmitter2,
    @Inject(forwardRef(() => ChatSessionService))
    private readonly chatSessionService: ChatSessionService,
    @Inject(forwardRef(() => CompaniesService))
    private readonly companyService: CompaniesService,
  ) {
    //this.restoreRemoteWhatsappSessionsWithLocal();
    this.initWhatsappClient();
  }

  //@Cron(CronExpression.EVERY_10_MINUTES)
  async asyncLocalSessionWithRemoteStore() {
    if (existsSync(this.authPath)) {
      try {
        const folders = readdirSync(this.authPath);

        for (const folder of folders) {
          this.digitalOceanService.uploadMultipleFilesToBucket(
            `${process.env.INIT_CWD}/.whatsapp-auth/${folder}`,
            'whatsapp-auth',
            folder,
          );
        }
      } catch (error) {
        Logger.error('error uploading whatsapp sessions', error);
      }
    }
  }

  async readFilesInFolder(folder: string) {
    const files = readdirSync(
      `${process.env.INIT_CWD}/.whatsapp-auth/${folder}`,
    );
    return files;
  }

  async deleteWhatsappSession(botRequestId: string) {
    try {
      this.runningClients.delete(botRequestId);
      this.activeCompanies.delete(botRequestId);

      await rimraf(
        `${process.env.INIT_CWD}/.whatsapp-auth/session-${botRequestId}`,
      ).catch((error) => {
        Logger.error('error deleting whatsapp session', error);
      });

      await this.changeWhatsappConnectionStatus(
        WhatsappAuthStatus.DISCONNECTED,
        botRequestId,
      );

      Logger.debug(`whatsapp session ${botRequestId} deleted`);
    } catch (error) {
      Logger.error('error deleting whatsapp session', error);
    }
  }

  async restoreRemoteWhatsappSessionsWithLocal() {
    try {
      Logger.debug('restoring local whatsapp sessions');
      const whatsappBots = await this.botCompanyRepository.find();

      for await (const bot of whatsappBots) {
        await this.digitalOceanService.downloadFileFromBucket(
          'whatsapp-auth',
          `session-${bot.botRequestId}`,
        );
      }

      Logger.debug('local whatsapp sessions restored');
      this.eventEmitter.emit('init-whatsapp-client');
    } catch (error) {
      Logger.error('error restoring local whatsapp sessions', error);
    }
  }

  @OnEvent('init-whatsapp-client')
  async initWhatsappClient() {
    const bots = await this.whatsappConfigRepository.find({
      relations: ['botCompany', 'botCompany.company'],
    });

    for (const bot of bots) {
      if (bot.connectionStatus === WhatsappAuthStatus.LINKED) {
        this.runWhatsappClients(bot?.botCompany?.botRequestId);
      }
    }

    Logger.debug('init whatsapp client');
  }

  async generateNewWhatsappClient(botRequestId: string) {
    if (!botRequestId) return;

    try {
      this.runWhatsappClients(botRequestId);
    } catch (error) {
      Logger.error('error running whatsapp client', error);
    }
  }

  async runWhatsappClients(botRequestId: string) {
    try {
      if (this.runningClients.has(botRequestId)) {
        console.log('client already running', botRequestId);
        return;
      }
      const company =
        await this.companyService.getCompanyByBotRequestId(botRequestId);

      if (!company) {
        return;
      }

      const whatsappClient = new whatsappWebJs.Client({
        authStrategy: new whatsappWebJs.LocalAuth({
          clientId: botRequestId,
          dataPath: '.whatsapp-auth',
        }),
        restartOnAuthFail: true,
        qrMaxRetries: 2,
        takeoverOnConflict: true,
        puppeteer: {
          headless: true,
          args: [
            '--disable-gpu',
            '--no-sandbox',
            '--disable-dev-shm-usage',
            '--disable-setuid-sandbox',
            '--single-process',
            '--disable-background-networking',
            '--disable-background-timer-throttling',
            '--disable-backgrounding-occluded-windows',
          ],
        },
      });

      console.log(`open client ${botRequestId}`);

      this.runningClients.set(botRequestId, whatsappClient);
      this.activeCompanies.add(botRequestId);
      await this.changeWhatsappConnectionStatus(
        WhatsappAuthStatus.CONNECTING,
        botRequestId,
      );

      whatsappClient.on('ready', async () => {
        await this.updateWhatsappClientInfo(
          botRequestId,
          whatsappClient.info.pushname,
          whatsappClient.info.wid.user,
        );

        this.runningClients.set(botRequestId, whatsappClient);

        await this.changeWhatsappConnectionStatus(
          WhatsappAuthStatus.LINKED,
          botRequestId,
        );
        Logger.debug(`bot whatsapp is up: ${botRequestId}`);
      });

      whatsappClient.on('authenticated', async () => {
        console.log('authenticated', botRequestId);
      });

      whatsappClient.on('auth_failure', async () => {
        whatsappClient.destroy();
        this.deleteWhatsappSession(botRequestId);

        console.log('error restoring whatsapp client', botRequestId);
      });

      whatsappClient.on('qr', async (qr) => {
        if (!company) {
          console.log('closing whatsapp client', botRequestId);
          whatsappClient.destroy();
          this.deleteWhatsappSession(botRequestId);
          return;
        }

        const activeCompanyExist = this.activeCompanies.has(botRequestId);

        if (activeCompanyExist) {
          this.changeWhatsappConnectionStatus(
            WhatsappAuthStatus.QR_GENERATED,
            botRequestId,
          );

          console.log('qr generated', botRequestId);

          this.eventEmitter.emit(WhatsappEvents.QR, { qr, botRequestId });
        } else {
          this.activeCompanies.delete(botRequestId);
          this.deleteWhatsappSession(botRequestId);
        }
      });

      whatsappClient.on('disconnected', () => {
        whatsappClient.destroy();
        this.deleteWhatsappSession(botRequestId);
      });

      whatsappClient.on('message', async (message) => {
        await this.handleWhatsappMessages(
          botRequestId,
          message,
          whatsappClient,
        );
      });

      whatsappClient.initialize();
    } catch (error) {
      console.log(error);
    }
  }

  async changeWhatsappConnectionStatus(
    status: WhatsappAuthStatus,
    botRequestId: string,
  ) {
    console.log('changing whatsapp connection status of', botRequestId, status);
    const whatsappConfig = await this.whatsappConfigRepository.findOne({
      where: { botCompany: { botRequestId } },
    });

    if (whatsappConfig) {
      whatsappConfig.connectionStatus = status;

      if (status === WhatsappAuthStatus.DISCONNECTED) {
        whatsappConfig.isConnected = false;
      }

      if (status === WhatsappAuthStatus.LINKED) {
        whatsappConfig.isConnected = true;
      }

      await this.whatsappConfigRepository.save(whatsappConfig);

      this.eventEmitter.emit(WhatsappEvents.STATE, {
        state: whatsappConfig.connectionStatus,
        botRequestId,
      });
    }
  }

  async getWhatsappConnectionState(
    botRequestId: string,
  ): Promise<{ state: WhatsappAuthStatus }> {
    const whatsappConfig = await this.whatsappConfigRepository.findOne({
      where: { botCompany: { botRequestId } },
      relations: ['botCompany', 'botCompany.company'],
    });

    if (!whatsappConfig) {
      return { state: WhatsappAuthStatus.DISCONNECTED };
    }

    this.activeCompanies.add(botRequestId);

    return { state: whatsappConfig.connectionStatus };
  }

  async updateWhatsappClientInfo(
    botRequestId: string,
    phoneNumberName: string,
    phoneNumber: string,
  ) {
    const whatsappConfig = await this.whatsappConfigRepository.findOne({
      where: { botCompany: { botRequestId } },
    });

    if (whatsappConfig) {
      whatsappConfig.phoneName = phoneNumberName;
      whatsappConfig.phoneNumber = phoneNumber;

      this.whatsappConfigRepository.save(whatsappConfig);
    }
  }

  async disconnectWhatsapp(botRequestId: string) {
    const whatsappClient = this.runningClients.get(botRequestId);

    if (whatsappClient) {
      const whatsappConfig = await this.whatsappConfigRepository.findOne({
        where: { botCompany: { botRequestId } },
      });

      console.log(whatsappConfig);
      if (whatsappConfig.connectionStatus !== WhatsappAuthStatus.LINKED) {
        try {
          whatsappClient?.destroy();
          this.deleteWhatsappSession(botRequestId);
        } catch (err) {
          Logger.error(err);
        }
      }
    }
  }

  async handleWhatsappMessages(
    botRequestId: string,
    message: whatsappWebJs.Message,
    client: whatsappWebJs.Client,
  ) {
    const allowedMessageType = [
      whatsappWebJs.MessageTypes.TEXT,
      whatsappWebJs.MessageTypes.IMAGE,
    ];
    const isGroup = message.from.endsWith('@g.us');
    const fromNumber = message.from.split('@')[0];
    const hasMedia = message.hasMedia;
    let media;

    if (
      isGroup ||
      !allowedMessageType.includes(message.type) ||
      message.isStatus
    ) {
      return;
    }

    if (hasMedia) {
      try {
        const whatsappMedia = await message.downloadMedia();
        media = `data:${whatsappMedia?.mimetype};base64,${whatsappMedia?.data}`;
      } catch (error) {
        Logger.error(error);
      }
    }

    const whatsappConfig = await this.whatsappConfigRepository.findOne({
      where: { botCompany: { botRequestId } },
      relations: ['botCompany'],
    });

    const isEnglish = false;

    const whatsappChat = await this.whatsappChatRepository.findOne({
      where: { fromPhoneNumber: fromNumber },
      relations: ['chatSession'],
      order: { createdAt: 'DESC' },
    });

    const chatSession = await this.chatSessionService.initChatSession(
      botRequestId,
      isEnglish,
      whatsappChat?.chatSession?.clientId || null,
    );

    const chatExists = await this.whatsappChatRepository.findOne({
      where: {
        chatSession: { clientId: chatSession.sessionId },
      },
    });

    if (!chatExists) {
      const newSessionToSave = new WhatsappChat();
      newSessionToSave.fromPhoneNumber = fromNumber;
      newSessionToSave.whatsappConfig = whatsappConfig;
      newSessionToSave.chatSession = chatSession.chat;
      newSessionToSave.chatId = message.from;

      await this.whatsappChatRepository.save(newSessionToSave);
    }

    const botCompany = await this.botCompanyRepository.findOne({
      where: { botRequestId },
      relations: ['company'],
    });

    let messageContent = message.body;

    if (hasMedia) {
      messageContent = media;
    }

    console.log('new message from', fromNumber, 'message', messageContent);
    const response = await this.chatSessionService.handleMessage(
      messageContent,
      botCompany,
      chatSession.sessionId,
      messageRole.USER,
      fromNumber,
      message.type,
    );

    if (response) {
      client.sendMessage(message.from, response);
    }
  }

  async createWhatsappConfig(botCompany: BotCompany) {
    try {
      return this.whatsappConfigRepository.save({
        botCompany,
        connectionStatus: WhatsappAuthStatus.DISCONNECTED,
        isConnected: false,
      });
    } catch (error) {
      console.log(error);
    }
  }

  async responseMessage(
    botRequestId: string,
    sessionId: string,
    message: string,
  ) {
    const client = this.runningClients.get(botRequestId);
    const whatsappChat = await this.whatsappChatRepository.findOne({
      where: { chatSession: { clientId: sessionId } },
    });

    if (client) {
      client.sendMessage(whatsappChat.chatId, message);
    }
  }
}
