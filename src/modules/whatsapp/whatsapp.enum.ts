export enum WhatsappAuthStatus {
  CONNECTING = 'connecting',
  DISCONNECTED = 'disconnected',
  QR_GENERATED = 'new_qr_received',
  LINKED = 'linked',
  QR_GENERATE_LIMIT_EXCEEDED = 'qr_generate_limit_exceeded',
}

export enum WhatsappMessageType {
  GROUP = 'g.us',
  PERSON = 's.whatsapp.net',
}

export enum WhatsappEvents {
  QR = 'qr',
  AUTH_FAILURE = 'auth_failure',
  AUTHENTICATED = 'authenticated',
  AUTH_LOGGED_IN = 'auth_logged_in',
  DISCONNECTED = 'disconnected',
  STATE = 'state',
  AGENT_CONNECTED = 'agent_connected',
  GENERATE_QR = 'generate_qr',
  CONNECT_AGENT = 'connect_agent',
}
