import {
  <PERSON>umn,
  CreateDateColumn,
  <PERSON>tity,
  <PERSON>in<PERSON><PERSON>um<PERSON>,
  OneToMany,
  OneToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';

import { BotCompany } from '../bot-company/bot-company.entity';
import { WhatsappAuthStatus } from './whatsapp.enum';
import { WhatsappChat } from './whatsapp-chat.entity';

@Entity()
export class WhatsappConfig {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ nullable: true })
  whatsappSessionId: string;

  @OneToOne(() => BotCompany, (botCompany) => botCompany.whatsappConfig)
  botCompany: BotCompany;

  @Column({ type: 'boolean', default: false })
  isConnected: boolean;

  @Column({
    type: 'enum',
    enum: WhatsappAuthStatus,
    default: WhatsappAuthStatus.DISCONNECTED,
  })
  connectionStatus: WhatsappAuthStatus;

  @Column({ type: 'text', nullable: true })
  qr: string;

  @Column({ nullable: true })
  phoneNumber: string;

  @Column({ nullable: true })
  phoneName: string;

  @OneToMany(
    () => WhatsappChat,
    (whatsappChat) => whatsappChat.whatsappConfig,
    {
      cascade: true,
    },
  )
  @JoinColumn()
  chats: WhatsappChat[];

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
