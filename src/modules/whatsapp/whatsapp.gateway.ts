import { Logger } from '@nestjs/common';
import { EventEmitter2, OnEvent } from '@nestjs/event-emitter';
import {
  SubscribeMessage,
  WebSocketGateway,
  OnGatewayInit,
  WebSocketServer,
  OnGatewayConnection,
  OnGatewayDisconnect,
  MessageBody,
  ConnectedSocket,
} from '@nestjs/websockets';
import { Server, Socket } from 'socket.io';
import { WhatsappAuthStatus, WhatsappEvents } from './whatsapp.enum';
import { WhatsappService } from './whatsapp.service';
import { CompaniesService } from '../companies/companies.service';

@WebSocketGateway({ transports: ['websocket'] })
export class WhatsappGateway
  implements OnGatewayInit, OnGatewayConnection, OnGatewayDisconnect
{
  constructor(
    private whatsappService: WhatsappService,
    private eventEmiiter: EventEmitter2,
    private companyService: CompaniesService,
  ) {}

  @WebSocketServer()
  server: Server;

  private clients: Map<string, Set<Socket>> = new Map();

  private agentConnectedClientsByBotRequestId: Map<string, Set<string>> =
    new Map();

  afterInit(server: Server) {
    Logger.log('Whatsapp socket Initialized');
    this.server = server;

    server.on('error', (error) => {
      Logger.error('Conexión WebSocket rechazada por CORS:', error);
    });
  }

  handleConnection(client: Socket, ...args: any[]) {
    Logger.debug(`user for whatsapp websocket connected: ${client.id}`);
  }

  async handleDisconnect(client: Socket) {
    Logger.error(`user for whatsapp websocket disconnected: ${client.id}`);
  }

  @SubscribeMessage(WhatsappEvents.DISCONNECTED)
  async disconnect(
    @ConnectedSocket() client: Socket,
    @MessageBody() data: { botRequestId: string },
  ) {
    console.log(`Client disconnected: ${client.id}`);
    this.clients.delete(data.botRequestId);

    this.whatsappService.disconnectWhatsapp(data?.botRequestId);
  }

  @SubscribeMessage(WhatsappEvents.GENERATE_QR)
  async generateQR(
    @ConnectedSocket() client: Socket,
    @MessageBody() data: { botRequestId: string },
  ) {
    if (!data.botRequestId) return;

    try {
      this.whatsappService.generateNewWhatsappClient(data?.botRequestId);
    } catch (error) {
      Logger.error(error);
    }
  }

  @SubscribeMessage(WhatsappEvents.CONNECT_AGENT)
  async handleAgentConnected(
    @ConnectedSocket() client: Socket,
    @MessageBody() data: { botRequestId: string },
  ) {
    try {
      const clientExist = this.clients.get(data.botRequestId);

      if (!clientExist) {
        this.clients.set(data.botRequestId, new Set([client]));
      }

      const state = await this.whatsappService.getWhatsappConnectionState(
        data.botRequestId,
      );

      client.emit(WhatsappEvents.STATE, state);
      client.emit(WhatsappEvents.AGENT_CONNECTED, true);

      const agentsConnected = this.agentConnectedClientsByBotRequestId.get(
        data.botRequestId,
      );

      if (agentsConnected) {
        agentsConnected.add(client.id);
      } else {
        this.agentConnectedClientsByBotRequestId.set(
          data.botRequestId,
          new Set([client.id]),
        );
      }
    } catch (error) {
      client.emit(WhatsappEvents.AGENT_CONNECTED, false);
    }
  }

  @SubscribeMessage(WhatsappEvents.STATE)
  async getWhatsappInformation(
    @ConnectedSocket() client: Socket,
    @MessageBody() data: { botRequestId: string },
  ) {
    const response = await this.whatsappService.getWhatsappConnectionState(
      data?.botRequestId,
    );

    client.emit(WhatsappEvents.STATE, response);
  }

  @OnEvent(WhatsappEvents.QR)
  async sendQr(payload) {
    const agentConnected = this.agentConnectedClientsByBotRequestId.get(
      payload.botRequestId,
    );

    if (agentConnected) {
      agentConnected.forEach((client) => {
        this.server.to(client).emit(WhatsappEvents.QR, payload);
      });
    }
  }

  @OnEvent(WhatsappEvents.STATE)
  async sendState(payload: {
    botRequestId: string;
    state: WhatsappAuthStatus;
  }) {
    const agentConnected = this.agentConnectedClientsByBotRequestId.get(
      payload.botRequestId,
    );

    if (agentConnected) {
      agentConnected.forEach((client) => {
        this.server.to(client).emit(WhatsappEvents.STATE, payload);
      });
    }
  }
}
