import { Controller, Get, Post, Body, Param, Query } from '@nestjs/common';

import { WhatsappService } from './whatsapp.service';
import { GenerateQr } from './whatsapp.dto';

@Controller('whatsapp')
export class WhatsappController {
  constructor(private whatsappService: WhatsappService) {}

  @Get('webhook')
  validateWebhook(@Query() query) {
    return query['hub.challenge'];
  }

  @Post('qr')
  generateQR(@Body() body: GenerateQr) {
    // return this.whatsappService.generateQRByBotRequestId(body.botRequestId);
  }

  @Get('qr/:botRequestId')
  getQR(@Param() param: { botRequestId: string }) {
    // return this.whatsappService.getQRByBotRequestId(param.botRequestId);
  }

  @Get('link/status/:botRequestId')
  getLinkStatus(@Param() param: { botRequestId: string }) {
    // return this.whatsappService.returnAuthSessionStatus(param.botRequestId);
  }

  @Post('new')
  async addNew() {
    // this.whatsappService.newWhatsappClient('aa22');
  }
}
