import { Module, forwardRef } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

import { WhatsappService } from './whatsapp.service';
import { WhatsappController } from './whatsapp.controller';
import { MessageEntryPointModule } from '../message-entry-point/message-entry-point.module';
import { BotCompany } from '../bot-company/bot-company.entity';
import { WhatsappConfig } from './whatsapp-config.entity';
import { DigitalOceanModule } from '../digital-ocean/digital-ocean.module';
import { WhatsappGateway } from './whatsapp.gateway';
import { ChatSessionModule } from '../chat-session/chat-session.module';
import { WhatsappChat } from './whatsapp-chat.entity';
import { CompaniesModule } from '../companies/companies.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([BotCompany, WhatsappConfig, WhatsappChat]),
    forwardRef(() => DigitalOceanModule),
    forwardRef(() => CompaniesModule),
    forwardRef(() => ChatSessionModule),
  ],
  providers: [WhatsappService, WhatsappGateway],
  exports: [WhatsappService],
  controllers: [WhatsappController],
})
export class WhatsappModule {}
