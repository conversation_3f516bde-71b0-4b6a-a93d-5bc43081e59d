import { BadRequestException, Logger } from '@nestjs/common';
import {
  SubscribeMessage,
  WebSocketGateway,
  OnGatewayInit,
  WebSocketServer,
  OnGatewayConnection,
  OnGatewayDisconnect,
  MessageBody,
  ConnectedSocket,
} from '@nestjs/websockets';
import { Server, Socket } from 'socket.io';

import { EventNames } from '../../utils/enums/eventNames.enum';
import { ActiveUserService } from '../active-user/active-user.service';
import {
  ChatConnected,
  InitBot,
  NewMessage,
} from './message-entry-point.interface';
import { MessageEntryPointService } from './message-entry-point.service';
import { RoomsService } from '../rooms/rooms.service';
import { CompaniesService } from '../companies/companies.service';

@WebSocketGateway({ transports: ['websocket'] })
export class MessageEntryPointGateway
  implements OnGatewayInit, OnGatewayConnection, OnGatewayDisconnect
{
  constructor(
    private botService: MessageEntryPointService,
    private activeUserService: ActiveUserService,
    private roomsService: RoomsService,
    private companiesService: CompaniesService,
  ) {}

  @WebSocketServer()
  server: Server;

  afterInit(server: Server) {
    Logger.log('message entry point websocket Initialized');
    this.server = server;

    server.on('error', (error) => {
      Logger.error('Conexión WebSocket rechazada por CORS:', error);
    });
  }

  handleConnection(client: Socket, ...args: any[]) {
    Logger.debug(
      `user for message entry point websocket connected: ${client.id}`,
      client.handshake.address,
    );
    // console.log(`Client connected: ${client.handshake.address}`);
  }

  async handleDisconnect(client: Socket) {
    console.log(`Client disconnected: ${client.id}`);
    await this.activeUserService.desactiveUser(client.id);
  }

  @SubscribeMessage(EventNames.INIT)
  async handleEvent(
    @ConnectedSocket() client: Socket,
    @MessageBody() initMessage: InitBot,
  ) {
    try {
      const response = await this.botService.initWebMessageEntryPoint(
        initMessage.provider,
        initMessage.botRequestId,
        initMessage.isEnglish,
        initMessage.sessionId,
      );

      if (!initMessage.botRequestId) {
        throw new BadRequestException('Bot request id is required');
      }

      await this.roomsService.joinRoom(client, initMessage.sessionId);
      client.emit(EventNames.INIT_SUCCESS, response);

      this.activeUserService.createActiveUser(
        initMessage.botRequestId,
        client.id,
      );
    } catch (error) {
      console.log(error);
      client.emit(EventNames.INIT_ERROR, { error: error.message });
    }
  }

  @SubscribeMessage(EventNames.MESSAGE)
  async handleMessage(
    @ConnectedSocket() client: Socket,
    @MessageBody() data: NewMessage,
  ): Promise<void> {
    if (
      !data?.sessionId ||
      !data?.botRequestId ||
      !data?.message ||
      !data?.role
    ) {
      client.emit(EventNames.MESSAGE_ERROR, {
        error: 'bad request',
      });

      return;
    }

    try {
      const response = await this.botService.handleBotMessage(
        data.message,
        data.sessionId,
        data.role,
      );
      const company = await this.companiesService.getCompanyBySessionId(
        data.sessionId,
      );

      client.emit(EventNames.MESSAGE_SUCCESS, response);

      this.roomsService.emitToRoom(
        `${company.id}`,
        EventNames.NOTIFICATION_MESSAGE_NEW,
        response,
        this.server,
      );
    } catch (error) {
      console.log(error);
      client.emit(EventNames.MESSAGE_ERROR, {
        error: error.message,
      });
    }
  }

  @SubscribeMessage(EventNames.CHAT_CONNECTED)
  async handleChatConnected(
    @ConnectedSocket() client: Socket,
    @MessageBody() data: ChatConnected,
  ) {
    const currentChatSession = await this.botService.getChatSessionBySessionId(
      data.sessionId,
    );

    client.join(data.sessionId);

    client.emit(EventNames.CHAT_CONNECTED, currentChatSession);
  }

  @SubscribeMessage(EventNames.CHAT_HISTORY)
  async getChatHistory(
    @ConnectedSocket() client: Socket,
    @MessageBody()
    data: {
      sessionId: string;
    },
  ) {
    const chatHistory = await this.botService.getChatSessionBySessionId(
      data.sessionId,
    );

    client.join(data.sessionId);

    this.server.to(data.sessionId).emit(EventNames.CHAT_HISTORY, chatHistory);
  }

  @SubscribeMessage('endConversation')
  handleEndConversation(@MessageBody() data: string): string {
    // get conversation id from the message, update the state of the conversation to ended and return a ended conversation message
    return data;
  }
}
