import { Module, forwardRef } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

import { MessageEntryPointService } from './message-entry-point.service';
import { MessageEntryPointController } from './message-entry-point.controller';
import { MessageEntryPointGateway } from './message-entry-point.gateway';
import { MessageEntryPoint } from './message-entry-point.entity';
import { BotCompanyModule } from '../bot-company/bot-company.module';
import { ChatSessionModule } from '../chat-session/chat-session.module';
import { ActiveUserModule } from '../active-user/active-user.module';
import { RoomsModule } from '../rooms/rooms.module';
import { CompaniesModule } from '../companies/companies.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([MessageEntryPoint]),
    forwardRef(() => BotCompanyModule),
    forwardRef(() => ChatSessionModule),
    forwardRef(() => ActiveUserModule),
    forwardRef(() => RoomsModule),
    forwardRef(() => CompaniesModule),
  ],
  providers: [MessageEntryPointService, MessageEntryPointGateway],
  controllers: [MessageEntryPointController],
  exports: [MessageEntryPointService],
})
export class MessageEntryPointModule {}
