import { ApiProperty } from '@nestjs/swagger';
import { messageRole } from '../chat-response-generator/chat-response-generator.enum';

export class Message {
  @ApiProperty()
  readonly content: string;

  @ApiProperty({ enum: messageRole })
  role: messageRole;
}

export class BotInitConversationDto {
  @ApiProperty()
  readonly sessionId: string;

  @ApiProperty()
  readonly message: string;
}

export class BotGetConversationDto {
  @ApiProperty()
  readonly sessionId: string;
}

export class BotConversationDto {
  @ApiProperty()
  readonly sessionId: string;

  @ApiProperty()
  readonly message: Message;
}
