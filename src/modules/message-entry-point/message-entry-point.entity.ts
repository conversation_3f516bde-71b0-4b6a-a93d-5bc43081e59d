import {
  <PERSON><PERSON><PERSON>,
  <PERSON>umn,
  PrimaryGeneratedColumn,
  OneToOne,
  CreateDateColumn,
  UpdateDateColumn,
  JoinColumn,
} from 'typeorm';

import { ChatSession } from '../chat-session/chat-session.entity';

@Entity()
export class MessageEntryPoint {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  channel: string;

  @OneToOne(() => ChatSession, (chatSession) => chatSession.messageEntryPoint, {
    nullable: true,
    onDelete: 'CASCADE',
  })
  @JoinColumn()
  chatSession: ChatSession;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
