import {
  BadRequestException,
  Inject,
  Injectable,
  forwardRef,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';

import { ChatProvider } from '../../utils/enums/chatProvider';
import { BotCompanyService } from '../bot-company/bot-company.service';
import { ChatSessionService } from '../chat-session/chat-session.service';
import { MessageEntryPoint } from './message-entry-point.entity';
import { messageRole } from '../chat-response-generator/chat-response-generator.enum';

@Injectable()
export class MessageEntryPointService {
  constructor(
    @InjectRepository(MessageEntryPoint)
    private messageEntryPointRepository: Repository<MessageEntryPoint>,
    @Inject(forwardRef(() => ChatSessionService))
    private chatSessionService: ChatSessionService,
    @Inject(forwardRef(() => BotCompanyService))
    private botCompanyService: BotCompanyService,
  ) {}

  async initWebMessageEntryPoint(
    provider: ChatProvider,
    botRequestId: string,
    isEnglish: boolean,
    sessionId?: string | null,
  ) {
    if (!Object.values(ChatProvider).includes(provider)) {
      throw new BadRequestException('Invalid provider');
    }

    const botCompany = await this.botCompanyService.getBotCompanyByBotRequestId(
      botRequestId,
    );

    if (!botCompany?.company) {
      throw new BadRequestException('Invalid botRequestId');
    }

    const newInitConversation = await this.chatSessionService.initChatSession(
      botRequestId,
      isEnglish,
      sessionId,
    );

    if (!sessionId) {
      const messageEntryPoint = new MessageEntryPoint();
      messageEntryPoint.channel = provider;
      messageEntryPoint.chatSession = newInitConversation.chat;
      await this.messageEntryPointRepository.save(messageEntryPoint);
    }

    return {
      quickReplies: botCompany.quickReplies,
      history: newInitConversation.history,
      botCompany,
      sessionId: newInitConversation.sessionId,
      configuration: {
        showWatermark: true,
        canChangeChatIcon: true,
      },
    };
  }

  async handleBotMessage(
    message: string,
    sessionId: string,
    role: messageRole,
  ) {
    const chatSession = await this.chatSessionService.getChatSessionBySessionId(
      sessionId,
    );

    const botCompany = chatSession?.botCompany;

    if (!botCompany?.company) {
      throw new BadRequestException('Invalid botRequestId');
    }

    const response = await this.chatSessionService.handleMessage(
      message,
      botCompany,
      sessionId,
      role,
    );

    return response;
  }

  async getMessagesEntryPointByBotRequestId(
    botRequestId: string,
  ): Promise<MessageEntryPoint[]> {
    return this.messageEntryPointRepository.find({
      where: { chatSession: { botCompany: { botRequestId } } },
      relations: ['chatSession', 'chatSession.botCompany'],
    });
  }

  async getChatSessionBySessionId(sessionId: string) {
    return this.chatSessionService.getChatSessionBySessionId(sessionId);
  }
}
