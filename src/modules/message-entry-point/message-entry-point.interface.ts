import { ChatProvider } from '../../utils/enums/chatProvider';
import { messageRole } from '../chat-response-generator/chat-response-generator.enum';

export interface InitBot {
  provider: ChatProvider;
  botRequestId: string;
  isEnglish: boolean;
  sessionId?: string;
}

export interface NewMessage {
  message: string;
  botRequestId: string;
  sessionId: string;
  role: messageRole;
  file?: string;
}

export interface Message {
  content: string;
  role: messageRole;
}

export interface ChatConnected {
  sessionId: string;
}
