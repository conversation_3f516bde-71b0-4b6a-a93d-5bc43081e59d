import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

import { TagsIdentity } from './tags-identity.entity';
import { TagsIdentityService } from './tags-identity.service';
import { TagsIdentityController } from './tags-identity.controller';

@Module({
  imports: [TypeOrmModule.forFeature([TagsIdentity])],
  providers: [TagsIdentityService],
  exports: [TagsIdentityService],
  controllers: [TagsIdentityController],
})
export class TagsIdentityModule {}
