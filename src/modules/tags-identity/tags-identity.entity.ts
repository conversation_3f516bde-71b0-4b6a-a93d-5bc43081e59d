import {
  Column,
  CreateDateColumn,
  Entity,
  ManyToMany,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { BotCompany } from '../bot-company/bot-company.entity';

@Entity()
export class TagsIdentity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  name: string;

  @Column({ type: 'text' })
  content: string;

  @ManyToMany(() => BotCompany, (botCompany) => botCompany.tagsIdentity)
  botCompany: BotCompany[];

  @UpdateDateColumn()
  updatedAt: Date;

  @CreateDateColumn()
  createdAt: Date;
}
