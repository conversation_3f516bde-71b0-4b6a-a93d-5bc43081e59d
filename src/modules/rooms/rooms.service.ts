import { Injectable, Logger } from '@nestjs/common';
import { Server, Socket } from 'socket.io';
import { CompaniesService } from '../companies/companies.service';
import { Company } from '../companies/companies.entity';

@Injectable()
export class RoomsService {
  private companyRooms: Map<string, Set<string>> = new Map();

  constructor(private companiesService: CompaniesService) {}

  async joinRoom(socket: Socket, sessionId?: string, companyId?: number) {
    let company: Company;

    if (sessionId) {
      company = await this.companiesService.getCompanyBySessionId(sessionId);
    } else {
      company = await this.companiesService.getCompanyByCompanyId(companyId);
    }

    if (!company) {
      return;
    }

    const roomId = `${company.id}`;

    if (!this.companyRooms.has(roomId)) {
      this.companyRooms.set(roomId, new Set());
    }

    return this.companyRooms.get(roomId).add(socket.id);
  }

  async joinAgentToRoom(socket: Socket, companyId: number) {
    const company =
      await this.companiesService.getCompanyByCompanyId(companyId);

    if (!company) {
      return;
    }

    const roomId = `${company.id}`;

    if (this.companyRooms.has(roomId)) {
      socket.join(roomId);
    } else {
      return null;
    }
  }

  leaveRoom(socket: Socket, roomName: string) {
    socket.leave(roomName);
    const room = this.companyRooms.get(roomName);
    if (room) {
      room.delete(socket.id);
      if (room.size === 0) {
        this.companyRooms.delete(roomName);
      }
    }
  }

  emitToRoom(roomName: string, event: string, data: any, server: Server) {
    server.to(roomName).emit(event, data);
    const companyRooms = this.companyRooms.get(roomName);

    if (companyRooms) {
      companyRooms.forEach((socketId) => {
        server.to(socketId).emit(event, data);
      });
    }
  }

  getAllRooms() {
    return Array.from(this.companyRooms.keys());
  }

  getClientsInRoom(roomName: string, server: Server): string[] {
    const adapter = server.of('/').adapter;
    const clients = adapter.rooms.get(roomName);
    if (clients) {
      return Array.from(clients);
    }
    return [];
  }
}
