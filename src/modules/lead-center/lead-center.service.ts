import { Inject, Injectable, Logger, forwardRef } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ChatSessionService } from '../chat-session/chat-session.service';
import { CompaniesService } from '../companies/companies.service';
import { OpenAiFunctionTools } from '../open-ai/open-ai.interface';

import { Board } from './board.entity';
import { TaskDto, UpdateLeadCriteriaDto } from './lead-center.dto';
import { LeadCenter } from './lead-center.entity';
import { Task } from './task.entity';
import { Company } from '../companies/companies.entity';
import { ContactService } from '../contact/contact.service';
import { OpenAiService } from '../open-ai/open-ai.service';
import { OnEvent } from '@nestjs/event-emitter';
import { ChatCompletionTool } from 'openai/resources';

@Injectable()
export class LeadCenterService {
  constructor(
    @InjectRepository(LeadCenter)
    private leadCenterRepository: Repository<LeadCenter>,
    @InjectRepository(Board) private boardRepository: Repository<Board>,
    @InjectRepository(Task) private taskRepository: Repository<Task>,
    @Inject(forwardRef(() => CompaniesService))
    private companyService: CompaniesService,
    @Inject(forwardRef(() => ChatSessionService))
    private chatSessionService: ChatSessionService,
    @Inject(forwardRef(() => ContactService))
    private contactService: ContactService,
    @Inject(forwardRef(() => OpenAiService))
    private openAiService: OpenAiService,
  ) {}

  async getLeadCenterByEmployeeId(employeeId: number) {
    const leadCenter = await this.leadCenterRepository.findOne({
      where: { company: { employees: { id: employeeId } } },
      relations: [
        'boards',
        'boards.tasks',
        'boards.tasks.chatSession',
        'boards.tasks.chatSession.botCompany',
      ],
      order: { boards: { sort: 'ASC' } },
    });

    if (!leadCenter) {
      return this.createLeadCenter(employeeId);
    }

    return leadCenter;
  }

  async getLeadCenterByCompanyId(companyId: number) {
    const leadCenter = await this.leadCenterRepository.findOne({
      where: { company: { id: companyId } },
      relations: [
        'boards',
        'boards.tasks',
        'boards.tasks.chatSession',
        'boards.tasks.chatSession.contact',
        'boards.tasks.chatSession.botCompany',
      ],
      order: { boards: { sort: 'ASC' } },
    });

    if (!leadCenter) {
      try {
        await this.createLeadCenter(undefined, companyId);
        return this.getLeadCenterByCompanyId(companyId);
      } catch (error) {
        Logger.error(error);
      }
    }

    return leadCenter;
  }

  @OnEvent('company.created')
  async createLeadCenterFromEvent(payload: { companyId: number }) {
    console.log(payload);
    await this.createLeadCenter(null, payload.companyId);
  }

  async createLeadCenter(employeeId?: number, companyId?: number) {
    let company: Company;

    if (employeeId) {
      company = await this.companyService.getCompanyByEmployeeId(employeeId);
    }

    if (companyId) {
      company = await this.companyService.getCompanyByCompanyId(companyId);
    }

    if (!company) {
      return null;
    }

    const todoBoard = new Board();
    todoBoard.title = 'todo';
    todoBoard.sort = 1;
    await this.boardRepository.save(todoBoard);

    const inProgressBoard = new Board();
    inProgressBoard.title = 'in progress';
    inProgressBoard.sort = 2;
    await this.boardRepository.save(inProgressBoard);

    const doneBoard = new Board();
    doneBoard.title = 'done';
    doneBoard.sort = 3;
    await this.boardRepository.save(doneBoard);

    const leadCenter = new LeadCenter();
    leadCenter.company = company;
    leadCenter.boards = [todoBoard, inProgressBoard, doneBoard];
    leadCenter.criteriaInputs = {
      email: false,
      name: false,
      phone: false,
    };

    return this.leadCenterRepository.save(leadCenter);
  }

  async createTask(boardId: number, task: TaskDto, chatSessionId: string) {
    const leadCenter = await this.leadCenterRepository.findOne({
      where: { boards: { id: boardId } },
    });

    if (!leadCenter) {
      const chatSession =
        await this.chatSessionService.getChatSessionBySessionId(chatSessionId);
      await this.createLeadCenter(undefined, chatSession.botCompany.company.id);
      this.createTask(boardId, task, chatSessionId);
    }

    const board = await this.boardRepository.findOne({
      where: { id: boardId },
    });

    if (!board) {
      return null;
    }

    const chatSession = await this.chatSessionService.getChatSessionBySessionId(
      chatSessionId,
    );

    if (!board) {
      return null;
    }

    const taskExists = await this.taskRepository.findOne({
      where: { chatSession: { id: chatSession.id } },
    });

    if (taskExists) {
      return taskExists;
    }

    const newTask = new Task();
    newTask.board = board;
    newTask.title = task?.title;
    newTask.aiDescription = task?.aiDescription;
    newTask.chatSession = chatSession;

    if (task?.tags) {
      await this.chatSessionService.addTagsToChatSessionByChatSessionId(
        chatSession.id,
        task.tags,
      );
    }

    return this.taskRepository.save(newTask);
  }

  async sortTasks(taskId: number, targetBoardId: number) {
    const task = await this.taskRepository.findOne({
      where: { id: taskId },
    });
    const board = await this.boardRepository.findOne({
      where: { id: targetBoardId },
    });

    task.board = board;
    return this.taskRepository.save(task);
  }

  async deleteTask(taskId: number) {
    return this.taskRepository.delete({ id: taskId });
  }

  async noPotentialClient(taskId: number) {
    const task = await this.taskRepository.findOne({
      where: { id: taskId },
      relations: ['chatSession'],
    });

    if (!task) {
      return null;
    }

    return this.deleteTask(taskId);
  }

  async markAsPotentialClient(chatSessionId: string) {
    const todoBoard = await this.boardRepository.findOne({
      where: {
        title: 'todo',
        leadCenter: {
          company: { botCompany: { chatSession: { clientId: chatSessionId } } },
        },
      },
      relations: ['tasks', 'tasks.chatSession', 'leadCenter'],
    });
    const contact =
      await this.contactService.getContactInformationByChatSessionId(
        chatSessionId,
      );
    const chatSession = await this.chatSessionService.getChatSessionBySessionId(
      chatSessionId,
    );

    const history = chatSession.chatResponseGenerator.map(
      (chatResponseGenerator) => {
        return {
          role: chatResponseGenerator.role,
          content: chatResponseGenerator.content,
        };
      },
    );

    const instructions = [
      {
        role: 'system',
        content: `a continuacion re pasare una conversacion con un usuario. Debes de darme resumen de por que este usuario fue marcado como un cliente potencial. el criterio para determinar que un cliente es potencial es: ${
          todoBoard.leadCenter.criteriaPotentialClient
        }. ---- historial --- ${JSON.stringify(history)}`,
      },
    ];

    const resume =
      await this.openAiService.getChatResumeForPotentialClientMarked(
        instructions,
      );

    const task: TaskDto = {
      title: contact?.name || chatSession?.clientId,
      aiDescription: resume?.choices[0]?.message?.content,
      tags: [],
    };

    await this.createTask(todoBoard.id, task, chatSessionId);
    await this.chatSessionService.toggleAiOrAgent(chatSessionId);
  }

  async updateCriteria(employeeId: number, criteria: UpdateLeadCriteriaDto) {
    const leadCenter = await this.leadCenterRepository.findOne({
      where: {
        company: { employees: { id: employeeId } },
      },
    });

    if (!leadCenter) {
      return;
    }

    leadCenter.criteriaInputs = criteria.criteriaInputs;
    leadCenter.criteriaPotentialClient = criteria.criteriaPotentialClient;

    return this.leadCenterRepository.save(leadCenter);
  }

  async getCriteriaFunctions(companyId: number): Promise<ChatCompletionTool[]> {
    const leadCenter = await this.leadCenterRepository.findOne({
      where: { company: { id: companyId } },
    });

    if (!leadCenter) {
      return;
    }

    const inputs = Object.entries(leadCenter.criteriaInputs)
      .filter(([_, value]) => value === true)
      .map(([key]) => key);

    const saveContactInformation: ChatCompletionTool = {
      type: 'function',
      function: {
        name: 'saveContactInformation',
        description: `when user provide there information like: ${inputs.join(
          ',',
        )} or contact number or email.`,
        parameters: {
          type: 'object',
          properties: inputs.reduce((acc, input) => {
            acc[input] = { type: 'string' };
            return acc;
          }, {}),
        },
      },
    };

    const criteria =
      'when the customer wants to acquire any product or service';

    const markChatAsPotentialClient: ChatCompletionTool = {
      type: 'function',
      function: {
        name: 'markChatAsPotentialClient',
        description: criteria,
        parameters: undefined,
      },
    };

    return [saveContactInformation, markChatAsPotentialClient];
  }
}
