import {
  Column,
  CreateDateColumn,
  Entity,
  <PERSON>inColumn,
  ManyToOne,
  OneToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';

import { Board } from './board.entity';
import { ChatSession } from '../chat-session/chat-session.entity';

@Entity()
export class Task {
  @PrimaryGeneratedColumn()
  id: number;

  @ManyToOne(() => Board, (board) => board.tasks)
  board: Board;

  @Column()
  title: string;

  @Column()
  aiDescription: string;

  @OneToOne(() => ChatSession, (chatSession) => chatSession.task, {
    onDelete: 'CASCADE',
  })
  @JoinColumn()
  chatSession: ChatSession;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
