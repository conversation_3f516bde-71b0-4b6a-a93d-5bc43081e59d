import { Transform } from 'class-transformer';
import { <PERSON><PERSON><PERSON><PERSON>, IsN<PERSON>ber, IsObject, IsString } from 'class-validator';

export class TaskDto {
  @IsString()
  title: string;

  @IsString()
  aiDescription: string;

  @IsArray()
  tags: string[];
}

export class CreateTaskDto {
  @IsNumber()
  @Transform(({ value }) => Number(value))
  boardId: number;

  @IsString()
  chatSessionId: string;

  task: TaskDto;
}

export class UpdateTaskDto {
  @IsNumber()
  @Transform(({ value }) => Number(value))
  taskId: number;

  @IsNumber()
  @Transform(({ value }) => Number(value))
  targetBoardId: number;
}

export class DeleteTaskDto {
  @IsNumber()
  @Transform(({ value }) => Number(value))
  taskId: number;
}

export class NoPotentialClientDto {
  @IsNumber()
  @Transform(({ value }) => Number(value))
  taskId: number;
}

export class UpdateLeadCriteriaDto {
  @IsObject()
  criteriaInputs: {
    [key: string]: boolean;
  };

  @IsString()
  criteriaPotentialClient: string;
}
