import {
  Column,
  CreateDateColumn,
  Entity,
  JoinColumn,
  OneToMany,
  OneToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { Company } from '../companies/companies.entity';
import { Board } from './board.entity';

@Entity()
export class LeadCenter {
  @PrimaryGeneratedColumn()
  id: number;

  @OneToOne(() => Company, (company) => company.leadCenter)
  @JoinColumn()
  company: Company;

  @OneToMany(() => Board, (board) => board.leadCenter)
  @JoinColumn()
  boards: Board[];

  @Column({ nullable: true, type: 'jsonb' })
  criteriaInputs: Record<string, boolean>;

  @Column({ type: 'text', nullable: true, default: '' })
  criteriaPotentialClient: string;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
