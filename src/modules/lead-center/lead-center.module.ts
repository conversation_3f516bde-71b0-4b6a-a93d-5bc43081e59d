import { Module, forwardRef } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

import { LeadCenter } from './lead-center.entity';
import { LeadCenterService } from './lead-center.service';
import { LeadCenterController } from './lead-center.controller';
import { Task } from './task.entity';
import { Board } from './board.entity';
import { CompaniesModule } from '../companies/companies.module';
import { ChatSessionModule } from '../chat-session/chat-session.module';
import { ContactModule } from '../contact/contact.module';
import { OpenAiModule } from '../open-ai/open-ai.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([LeadCenter, Task, Board]),
    forwardRef(() => CompaniesModule),
    forwardRef(() => ChatSessionModule),
    forwardRef(() => ContactModule),
    forwardRef(() => OpenAiModule),
  ],
  providers: [LeadCenterService],
  controllers: [LeadCenterController],
  exports: [LeadCenterService],
})
export class LeadCenterModule {}
