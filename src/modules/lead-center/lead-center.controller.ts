import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Put,
  Req,
  UseGuards,
} from '@nestjs/common';
import { AuthGuard } from '../auth/auth.guard';
import {
  CreateTaskDto,
  DeleteTaskDto,
  NoPotentialClientDto,
  UpdateLeadCriteriaDto,
  UpdateTaskDto,
} from './lead-center.dto';
import { LeadCenterService } from './lead-center.service';

@UseGuards(AuthGuard)
@Controller('lead-center')
export class LeadCenterController {
  constructor(private leadCenterService: LeadCenterService) {}

  @Get()
  getLeadCenter(@Req() req: any) {
    return this.leadCenterService.getLeadCenterByEmployeeId(req.user.sub);
  }

  @Post()
  createTask(@Body() body: CreateTaskDto) {
    return this.leadCenterService.createTask(
      body.boardId,
      body.task,
      body.chatSessionId,
    );
  }

  @Put('sort')
  updateTask(@Body() body: UpdateTaskDto) {
    return this.leadCenterService.sortTasks(body.taskId, body.targetBoardId);
  }

  @Delete('task/:taskId')
  deleteTask(@Param() params: DeleteTaskDto) {
    return this.leadCenterService.deleteTask(params.taskId);
  }

  @Put('task/no-potential-client/:taskId')
  noPotentialClient(@Param() params: NoPotentialClientDto) {
    return this.leadCenterService.noPotentialClient(params.taskId);
  }

  @Put('criteria')
  updateCriteria(@Body() body: UpdateLeadCriteriaDto, @Req() req) {
    return this.leadCenterService.updateCriteria(req.user.sub, body);
  }
}
