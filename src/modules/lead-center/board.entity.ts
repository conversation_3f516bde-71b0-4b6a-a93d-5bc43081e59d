import {
  Column,
  CreateDateColumn,
  Entity,
  JoinColumn,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';

import { LeadCenter } from './lead-center.entity';
import { Task } from './task.entity';

@Entity()
export class Board {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  title: string;

  @ManyToOne(() => LeadCenter, (leadCenter) => leadCenter.boards)
  leadCenter: LeadCenter;

  @OneToMany(() => Task, (task) => task.board, { nullable: true })
  @JoinColumn()
  tasks: Task[];

  @Column({ type: 'int', nullable: false })
  sort: number;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
