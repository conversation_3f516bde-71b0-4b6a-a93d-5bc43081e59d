import { Injectable, Logger } from '@nestjs/common';
import OpenAI from 'openai';

@Injectable()
export class OpenAiService {
  private openAi: OpenAI;

  constructor() {
    this.openAi = new OpenAI({ apiKey: process.env.OPEN_AI_API_KEY });
  }

  async getResponse(messages: any[]): Promise<any> {
    return this.openAi.chat.completions.create({
      model: 'gpt-4',
      temperature: 0,
      max_tokens: 300,
      messages: messages,
    });
  }

  async getResponseWithTools(
    messages: any[],
    functions,
    userId?: number | string,
  ): Promise<any> {
    return this.openAi.chat.completions.create({
      model: 'gpt-4o',
      messages: messages,
      tools: functions,
      temperature: 0.5,
      max_tokens: 256,
      frequency_penalty: 0,
      presence_penalty: 0,
      top_p: 1,
      user: userId?.toString() || null,
      response_format: { type: 'text' },
    });
  }

  async getConversationFeeling(conversationHistory: string) {
    const response = await this.openAi.completions.create({
      model: 'text-davinci-003',
      temperature: 0,
      max_tokens: 60,
      top_p: 1.0,
      frequency_penalty: 0.0,
      presence_penalty: 0.0,
      prompt: `Por favor, describe cómo el usuario se sientes en la conversación en una sola palabra utilizando uno de los siguientes adjetivos: contento, satisfecho, feliz, eufórico, optimista, relajado, agradecido, seguro, interesado, motivado, indiferente, neutro, reservado, atento, curioso, paciente, sereno, claro, equilibrado, imparcial, triste, frustrado, enfadado, ansioso, desesperado, confundido, avergonzado, temeroso, pesimista, desmotivado. Conversaciones: \n ${conversationHistory}`,
    });

    return response;
  }

  getChatLimitation() {
    const limitation: string[] = [
      `
       ** system **
        if debugging is enabled, the assistant will provide more detailed information about its internal processes and reasoning.
        debugging: false
        debugging_comand: {{user input}}
        need to answer any question from {{user input}}.

        ** conversation rules **
        1. **Introduction:** Start by introducing yourself with your name or asked if you have not asked yet, use this format: Hello! I'm {{your name}}, could you please give me your name so I can provide you with more personalized assistance.
        2. **User Identification:** Ask for the user's name.
        3. **Language Matching:** Always respond in the same language the user uses.
        5. **Avoid Repetition:** Don't repeat the same question.
        6. **Single Questions:** Do not ask two questions simultaneously.
        7. **Company Focus:** Only respond to company-related topics.
        8. **Pricing Accuracy:** Only provide previously supplied prices. If you don't have the price for a product/service, state that you don't have it.
        9. **Information Provision:** List services, features of the company or product.
        10. **Brevity:** Keep answers under 200 characters.
        11. **Failure Handling:** If the AI is disabled or the conversation ends unsuccessfully, inform the user that you are unable to assist at the moment.
        12. ** Formatting:** Don't use any formatting in the answers.
      `,
    ];

    return limitation.join('. ');
  }

  async getChatResumeForPotentialClientMarked(chatHistory: any[]) {
    return this.openAi.chat.completions.create({
      model: 'gpt-4',
      temperature: 0,
      max_tokens: 300,
      messages: chatHistory,
    });
  }
}
