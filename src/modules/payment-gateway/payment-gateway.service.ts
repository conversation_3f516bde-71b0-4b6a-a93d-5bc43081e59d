import { BadGatewayException, Injectable } from '@nestjs/common';
import Strip<PERSON> from 'stripe';
import { SubscribeDto } from './payment-gateway.dto';
import { CompaniesService } from '../companies/companies.service';
import { SubscriptionService } from '../subscription/subscription.service';
import { EmployeeService } from '../employee/employee.service';

@Injectable()
export class PaymentGatewayService {
  private stripeClient: Stripe;

  constructor(
    private companyService: CompaniesService,
    private subscribeService: SubscriptionService,
    private employeeService: EmployeeService,
  ) {
    this.stripeClient = new Stripe(
      'sk_test_51NZKOuBv9J2WILewa3vIj1V4XN4p0cd0cPrsaKDjnScIPrqiSmMij8I5StvcqGW63i4MLDeLWGhEHJBqIsjMt3Lc000M0mZEPk',
    );
  }

  async handleSubscription(employeeId: number, subscriptionData: SubscribeDto) {
    const company = await this.companyService.getCompanyByEmployeeId(
      employeeId,
    );

    if (!company) {
      throw new BadGatewayException('company not exist');
    }

    const employee = await this.employeeService.getOneEmployee(employeeId);

    if (!employee) {
      throw new BadGatewayException('user not exist');
    }

    const session = await this.stripeClient.checkout.sessions.create({
      line_items: [
        {
          price: subscriptionData.idIdentify,
          quantity: 1,
        },
      ],
      customer_email: employee.email,
      mode: 'subscription',
      success_url: `${process.env.FRONT_END_URL}/pricing?success=true&idIdentify=${subscriptionData.idIdentify}`,
      cancel_url: `${process.env.FRONT_END_URL}/pricing?success=false`,
      metadata: {
        companyId: company.id,
        employeeId: employeeId,
        employeeName: employee.name,
        employeeEmail: employee.email,
      },
    });

    return session.url;
  }

  async markUserAsPaid(employeeId: number, idIdentify: string) {
    const company = await this.companyService.getCompanyByEmployeeId(
      employeeId,
    );

    if (!company) {
      throw new BadGatewayException('company not exist');
    }

    const employee = await this.employeeService.getOneEmployee(employeeId);

    const subscribed = await this.subscribeService.createSubscription(
      company.id,
      idIdentify,
    );

    console.log(subscribed);
  }

  async cancelSubscription(
    employeeId: number,
    companyId: number,
    idIdentify: string,
  ) {
    const company = await this.companyService.getCompanyByEmployeeId(
      employeeId,
    );

    if (!company) {
      throw new BadGatewayException('company not exist');
    }

    if (company.id !== companyId) {
      throw new BadGatewayException('company not exist');
    }

    await this.subscribeService.cancelSubscription(idIdentify);
    return { canceled: true };
  }
}
