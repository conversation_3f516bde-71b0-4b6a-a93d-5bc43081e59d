import { Module } from '@nestjs/common';
import { PaymentGatewayService } from './payment-gateway.service';
import { PaymentGatewayController } from './payment-gateway.controller';
import { CompaniesModule } from '../companies/companies.module';
import { SubscriptionModule } from '../subscription/subscription.module';
import { EmployeeModule } from '../employee/employee.module';

@Module({
  imports: [CompaniesModule, SubscriptionModule, EmployeeModule],
  providers: [PaymentGatewayService],
  controllers: [PaymentGatewayController],
  exports: [PaymentGatewayService],
})
export class PaymentGatewayModule {}
