import { Body, Controller, Post, Req, Res, UseGuards } from '@nestjs/common';
import { PaymentGatewayService } from './payment-gateway.service';
import { SubscribeDto } from './payment-gateway.dto';
import { AuthGuard } from '../auth/auth.guard';

@UseGuards(AuthGuard)
@Controller('payment-gateway')
export class PaymentGatewayController {
  constructor(private paymentGatewayService: PaymentGatewayService) {}

  @Post('subscribe')
  async subscribe(@Req() req, @Body() body: SubscribeDto) {
    const paymentResponse = await this.paymentGatewayService.handleSubscription(
      req.user.sub,
      body,
    );

    return { redirect: paymentResponse };
  }

  @Post('mark-user-as-paid')
  async markUserAsPaid(@Req() req, @Body() body: SubscribeDto) {
    return await this.paymentGatewayService.markUserAsPaid(
      req.user.sub,
      body.idIdentify,
    );
  }

  @Post('cancel-subscription')
  async cancelSubscription(@Req() req, @Body() body: SubscribeDto) {
    return await this.paymentGatewayService.cancelSubscription(
      req.user.sub,
      req.user.company.id,
      body.idIdentify,
    );
  }
}
