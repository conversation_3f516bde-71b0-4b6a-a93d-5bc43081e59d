interface MessagesWebhookResponse {
  object: 'string';
  entry: Entry[];
}

interface Entry {
  id: string;
  changes: Changes[];
}

interface Changes {
  value: Value;
  field: string;
}

interface Metadata {
  display_phone_number: string;
  phone_number_id: string;
}

interface Contacts {
  profile: {};
  wa_id: string;
}

interface WhatsappMessage {
  from: string;
  id: string;
  timestamp: string;
  text: {
    body: string;
  };
  type: string;
}

interface Value {
  messaging_product: string;
  metadata: Metadata,
  contacts: Contacts[],
  messages: WhatsappMessage[]
}