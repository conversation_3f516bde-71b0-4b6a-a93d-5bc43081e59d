import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { config as dotenvConfig } from 'dotenv';
import { ScheduleModule } from '@nestjs/schedule';

import { AppController } from './app.controller';
import { AppService } from './app.service';
import { ChatResponseGeneratorModule } from './modules/chat-response-generator/chat-response-generator.module';
import { CompaniesModule } from './modules/companies/companies.module';
import { EmployeeModule } from './modules/employee/employee.module';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { AuthModule } from './modules/auth/auth.module';
import { MessageEntryPointModule } from './modules/message-entry-point/message-entry-point.module';
import { ChatSessionModule } from './modules/chat-session/chat-session.module';
import { JwtHandleModule } from './modules/jwt-handle/jwt-handle.module';
import { BotCompanyModule } from './modules/bot-company/bot-company.module';
import { SecretKeyModule } from './modules/secret-key/secret-key.module';
import { EmailModule } from './modules/email/email.module';
import { TagsIdentityModule } from './modules/tags-identity/tags-identity.module';
import { StatisticModule } from './modules/statistic/statistic.module';
import { ActiveUserModule } from './modules/active-user/active-user.module';
import { DigitalOceanModule } from './modules/digital-ocean/digital-ocean.module';
import { WhatsappModule } from './modules/whatsapp/whatsapp.module';
import { DataSourcesModule } from './modules/data-sources/data-sources.module';
import { WebModule } from './modules/web/web.module';
import { OpenAiModule } from './modules/open-ai/open-ai.module';
import { TeamManagementModule } from './modules/team-management/team-management.module';
import { dataSourceOptions } from './db/data-source';
import { InboxModule } from './modules/inbox/inbox.module';
import { LeadCenterModule } from './modules/lead-center/lead-center.module';
import { ContactModule } from './modules/contact/contact.module';
import { RoomsModule } from './modules/rooms/rooms.module';
import { SocialWebhookModule } from './modules/social-webhook/social-webhook.module';
import { SocialAuthModule } from './modules/social-auth/social-auth.module';
import { CalendarsModule } from './modules/calendars/calendars.module';
import { SubscriptionModule } from './modules/subscription/subscription.module';
import { PaymentGatewayModule } from './modules/payment-gateway/payment-gateway.module';
import { FacebookModule } from './modules/facebook/facebook.module';
import { OrderModule } from './modules/order/order.module';
import { ProductModule } from './modules/product/product.module';

dotenvConfig();

@Module({
  imports: [
    ScheduleModule.forRoot(),
    EventEmitterModule.forRoot(),
    ConfigModule.forRoot({
      isGlobal: true,
    }),
    TypeOrmModule.forRoot(dataSourceOptions),
    ChatResponseGeneratorModule,
    CompaniesModule,
    EmployeeModule,
    AuthModule,
    MessageEntryPointModule,
    ChatSessionModule,
    JwtHandleModule,
    TagsIdentityModule,
    BotCompanyModule,
    SecretKeyModule,
    EmailModule,
    StatisticModule,
    ActiveUserModule,
    DigitalOceanModule,
    WhatsappModule,
    DataSourcesModule,
    WebModule,
    OpenAiModule,
    TeamManagementModule,
    InboxModule,
    LeadCenterModule,
    ContactModule,
    RoomsModule,
    SocialWebhookModule,
    SocialAuthModule,
    CalendarsModule,
    SubscriptionModule,
    PaymentGatewayModule,
    FacebookModule,
    OrderModule,
    ProductModule,
  ],
  controllers: [AppController],
  providers: [AppService],
})
export class AppModule {}
