import { MigrationInterface, QueryRunner, Table, Index } from 'typeorm';

export class CreateBlacklistedTokensTable1700000000002
  implements MigrationInterface
{
  name = 'CreateBlacklistedTokensTable1700000000002';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Create blacklisted_tokens table
    await queryRunner.createTable(
      new Table({
        name: 'blacklisted_tokens',
        columns: [
          {
            name: 'id',
            type: 'int',
            isPrimary: true,
            isGenerated: true,
            generationStrategy: 'increment',
          },
          {
            name: 'token_id',
            type: 'varchar',
            length: '64',
            isUnique: true,
          },
          {
            name: 'user_id',
            type: 'int',
          },
          {
            name: 'reason',
            type: 'enum',
            enum: [
              'logout',
              'logout_all',
              'password_change',
              'security_breach',
              'admin_revoke',
              'token_expired',
            ],
            default: "'logout'",
          },
          {
            name: 'expires_at',
            type: 'timestamp',
          },
          {
            name: 'ip_address',
            type: 'varchar',
            length: '45',
            isNullable: true,
          },
          {
            name: 'user_agent',
            type: 'text',
            isNullable: true,
          },
          {
            name: 'additional_info',
            type: 'jsonb',
            isNullable: true,
          },
          {
            name: 'blacklisted_at',
            type: 'timestamp',
            default: 'CURRENT_TIMESTAMP',
          },
        ],
        foreignKeys: [
          {
            columnNames: ['user_id'],
            referencedTableName: 'employee',
            referencedColumnNames: ['id'],
            onDelete: 'CASCADE',
          },
        ],
      }),
      true,
    );

    // Create indexes for performance
    await queryRunner.query(`
      CREATE UNIQUE INDEX "IDX_blacklisted_tokens_token_id" ON "blacklisted_tokens" ("token_id");
      CREATE INDEX "IDX_blacklisted_tokens_user_id" ON "blacklisted_tokens" ("user_id");
      CREATE INDEX "IDX_blacklisted_tokens_expires_at" ON "blacklisted_tokens" ("expires_at");
      CREATE INDEX "IDX_blacklisted_tokens_reason" ON "blacklisted_tokens" ("reason");
      CREATE INDEX "IDX_blacklisted_tokens_created_at" ON "blacklisted_tokens" ("blacklisted_at");
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop table (indexes will be dropped automatically)
    await queryRunner.dropTable('blacklisted_tokens');
  }
}
