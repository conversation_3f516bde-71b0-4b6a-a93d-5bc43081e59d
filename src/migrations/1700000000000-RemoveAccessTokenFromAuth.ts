import { MigrationInterface, QueryRunner, TableColumn } from 'typeorm';

export class RemoveAccessTokenFromAuth1700000000000 implements MigrationInterface {
  name = 'RemoveAccessTokenFromAuth1700000000000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Remove the accessToken column from auth table
    // This improves security by not storing JWT tokens in the database
    await queryRunner.dropColumn('auth', 'accessToken');
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Add back the accessToken column if rollback is needed
    await queryRunner.addColumn(
      'auth',
      new TableColumn({
        name: 'accessToken',
        type: 'text',
        isNullable: true,
      }),
    );
  }
}
