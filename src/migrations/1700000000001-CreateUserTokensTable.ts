import { MigrationInterface, QueryRunner, Table, Index } from 'typeorm';

export class CreateUserTokensTable1700000000001 implements MigrationInterface {
  name = 'CreateUserTokensTable1700000000001';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Create user_tokens table
    await queryRunner.createTable(
      new Table({
        name: 'user_tokens',
        columns: [
          {
            name: 'id',
            type: 'int',
            isPrimary: true,
            isGenerated: true,
            generationStrategy: 'increment',
          },
          {
            name: 'token_id',
            type: 'varchar',
            length: '64',
            isUnique: true,
          },
          {
            name: 'user_id',
            type: 'int',
          },
          {
            name: 'device_info',
            type: 'text',
            isNullable: true,
          },
          {
            name: 'ip_address',
            type: 'varchar',
            length: '45',
            isNullable: true,
          },
          {
            name: 'is_active',
            type: 'boolean',
            default: true,
          },
          {
            name: 'expires_at',
            type: 'timestamp',
          },
          {
            name: 'last_used_at',
            type: 'timestamp',
            isNullable: true,
          },
          {
            name: 'created_at',
            type: 'timestamp',
            default: 'CURRENT_TIMESTAMP',
          },
          {
            name: 'updated_at',
            type: 'timestamp',
            default: 'CURRENT_TIMESTAMP',
            onUpdate: 'CURRENT_TIMESTAMP',
          },
        ],
        foreignKeys: [
          {
            columnNames: ['user_id'],
            referencedTableName: 'employee',
            referencedColumnNames: ['id'],
            onDelete: 'CASCADE',
          },
        ],
      }),
      true,
    );

    // Create indexes for performance
    await queryRunner.query(`
      CREATE UNIQUE INDEX "IDX_user_tokens_token_id" ON "user_tokens" ("token_id");
      CREATE INDEX "IDX_user_tokens_user_id_active" ON "user_tokens" ("user_id", "is_active");
      CREATE INDEX "IDX_user_tokens_expires_at" ON "user_tokens" ("expires_at");
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop table (indexes will be dropped automatically)
    await queryRunner.dropTable('user_tokens');
  }
}
