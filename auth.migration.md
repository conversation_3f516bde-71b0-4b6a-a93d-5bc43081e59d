# Guía de Migración de Autenticación - Frontend

## 📋 Resumen de Cambios

Esta guía documenta los cambios realizados en el sistema de autenticación del backend y las acciones necesarias en el frontend para mantener la compatibilidad.

### 🔄 **Versión de API**: v2.0.0
### 📅 **Fecha de Implementación**: Julio 2025
### ⚠️ **Nivel de Cambio**: BREAKING CHANGES

---

## 🚨 Cambios Críticos

### 1. **Tiempo de Expiración de Tokens**
- **Antes**: 60 días
- **Ahora**: 30 minutos por defecto
- **Impacto**: Los tokens expiran mucho más rápido

### 2. **Validaciones de Entrada Mejoradas**
- **Nuevo**: Validación estricta de formato de email
- **Nuevo**: Validación de longitud mínima de contraseña (6 caracteres)
- **Impacto**: Requests con datos inválidos serán rechazados

### 3. **Mensajes de Error Mejorados**
- **Cambio**: Mensajes más descriptivos y específicos
- **Impacto**: Actualizar manejo de errores en UI

---

## 📝 Cambios en Endpoints

### `POST /auth/login`

#### ✅ **Campos que NO cambiaron**
```json
{
  "email": "string",
  "password": "string"
}
```

#### 🔄 **Validaciones Nuevas**
- **Email**: Debe ser formato válido (regex: `/^[^\s@]+@[^\s@]+\.[^\s@]+$/`)
- **Password**: Mínimo 6 caracteres, máximo 100 caracteres

#### 📤 **Respuesta Actualizada**
```json
{
  "userData": {
    "id": 1,
    "name": "John Doe",
    "email": "<EMAIL>",
    "company": {
      "id": 1,
      "name": "Company Name"
    },
    "ability": [
      {
        "action": "manage",
        "subject": "all"
      }
    ]
    // ❌ REMOVIDO: password (ya no se incluye por seguridad)
  },
  "shouldUpdatePassword": false,
  "accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..." // ⚠️ Temporalmente igual que accessToken
}
```

#### 🚫 **Campos Eliminados de la Respuesta**
- `userData.password` - Removido por seguridad

---

## 🔧 Acciones Requeridas en Frontend

### 1. **Actualizar Validaciones de Formulario**

```javascript
// ❌ ANTES - Validación básica
const validateLogin = (email, password) => {
  return email && password;
};

// ✅ AHORA - Validación robusta
const validateLogin = (email, password) => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  
  if (!email || !password) {
    throw new Error('Email and password are required');
  }
  
  if (!emailRegex.test(email)) {
    throw new Error('Please provide a valid email address');
  }
  
  if (password.length < 6) {
    throw new Error('Password must be at least 6 characters long');
  }
  
  if (password.length > 100) {
    throw new Error('Password must not exceed 100 characters');
  }
  
  return true;
};
```

### 2. **Actualizar Manejo de Errores**

```javascript
// ✅ NUEVOS - Códigos de error específicos
const handleAuthError = (error) => {
  switch (error.message) {
    case 'Email and password are required':
      return 'Por favor complete todos los campos';
    
    case 'Please provide a valid email address':
      return 'Por favor ingrese un email válido';
    
    case 'Password must be at least 6 characters long':
      return 'La contraseña debe tener al menos 6 caracteres';
    
    case 'Invalid credentials':
      return 'Email o contraseña incorrectos';
    
    case 'Email not confirmed. Please check your email and confirm your account':
      return 'Por favor confirme su email antes de iniciar sesión';
    
    default:
      return 'Error de autenticación. Intente nuevamente';
  }
};
```

### 3. **Implementar Renovación Automática de Tokens**

```javascript
// ✅ NUEVO - Sistema de renovación automática
class AuthManager {
  constructor() {
    this.tokenExpirationTime = 30 * 60 * 1000; // 30 minutos en ms
    this.renewalBuffer = 5 * 60 * 1000; // Renovar 5 min antes
  }

  scheduleTokenRenewal(token) {
    const expirationTime = this.getTokenExpiration(token);
    const renewalTime = expirationTime - this.renewalBuffer;
    const timeUntilRenewal = renewalTime - Date.now();

    if (timeUntilRenewal > 0) {
      setTimeout(() => {
        this.renewToken();
      }, timeUntilRenewal);
    }
  }

  async renewToken() {
    try {
      // TODO: Implementar cuando esté disponible POST /auth/refresh
      console.log('Token renewal will be implemented in Phase 3');
    } catch (error) {
      // Redirigir a login si falla la renovación
      this.redirectToLogin();
    }
  }
}
```

### 4. **Actualizar Interceptores HTTP**

```javascript
// ✅ ACTUALIZADO - Interceptor con manejo mejorado
axios.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      // Token expirado o inválido
      localStorage.removeItem('accessToken');
      window.location.href = '/login';
    }
    
    if (error.response?.status === 400) {
      // Validación fallida - mostrar mensaje específico
      const message = error.response.data.message;
      showErrorMessage(handleAuthError({ message }));
    }
    
    return Promise.reject(error);
  }
);
```

---

## 📋 Ejemplo Completo de Implementación

### **Componente de Login Actualizado**

```javascript
import React, { useState } from 'react';
import axios from 'axios';

const LoginForm = () => {
  const [formData, setFormData] = useState({ email: '', password: '' });
  const [errors, setErrors] = useState({});
  const [loading, setLoading] = useState(false);

  const validateForm = () => {
    const newErrors = {};
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

    if (!formData.email) {
      newErrors.email = 'Email es requerido';
    } else if (!emailRegex.test(formData.email)) {
      newErrors.email = 'Por favor ingrese un email válido';
    }

    if (!formData.password) {
      newErrors.password = 'Contraseña es requerida';
    } else if (formData.password.length < 6) {
      newErrors.password = 'La contraseña debe tener al menos 6 caracteres';
    } else if (formData.password.length > 100) {
      newErrors.password = 'La contraseña no puede exceder 100 caracteres';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) return;

    setLoading(true);
    try {
      const response = await axios.post('/auth/login', {
        email: formData.email,
        password: formData.password
      });

      const { userData, accessToken, shouldUpdatePassword } = response.data;
      
      // Guardar token
      localStorage.setItem('accessToken', accessToken);
      
      // Configurar renovación automática
      const authManager = new AuthManager();
      authManager.scheduleTokenRenewal(accessToken);
      
      // Verificar si necesita actualizar contraseña
      if (shouldUpdatePassword) {
        window.location.href = '/change-password';
      } else {
        window.location.href = '/dashboard';
      }
      
    } catch (error) {
      const errorMessage = error.response?.data?.message || 'Error de conexión';
      setErrors({ general: handleAuthError({ message: errorMessage }) });
    } finally {
      setLoading(false);
    }
  };

  return (
    <form onSubmit={handleSubmit}>
      <div>
        <input
          type="email"
          placeholder="Email"
          value={formData.email}
          onChange={(e) => setFormData({...formData, email: e.target.value})}
          maxLength={254} // Límite estándar de email
        />
        {errors.email && <span className="error">{errors.email}</span>}
      </div>
      
      <div>
        <input
          type="password"
          placeholder="Contraseña"
          value={formData.password}
          onChange={(e) => setFormData({...formData, password: e.target.value})}
          minLength={6}
          maxLength={100}
        />
        {errors.password && <span className="error">{errors.password}</span>}
      </div>
      
      {errors.general && <div className="error">{errors.general}</div>}
      
      <button type="submit" disabled={loading}>
        {loading ? 'Iniciando sesión...' : 'Iniciar Sesión'}
      </button>
    </form>
  );
};

export default LoginForm;
```

---

## ⚠️ Consideraciones Importantes

### 1. **Compatibilidad Temporal**
- Los cambios son **retrocompatibles** en estructura de request
- Solo cambian las validaciones y mensajes de error

### 2. **Próximas Fases**
- **Fase 2**: Logout seguro con blacklist
- **Fase 3**: Refresh tokens automáticos
- **Fase 4**: Rate limiting y seguridad avanzada

### 3. **Variables de Entorno**
Asegurar que estas variables estén configuradas:
```env
ACCESS_TOKEN_EXPIRES_IN=30m
REFRESH_TOKEN_EXPIRES_IN=7d
EMAIL_TOKEN_EXPIRES_IN=24h
PASSWORD_RESET_TOKEN_EXPIRES_IN=1h
```

---

## 🧪 Testing

### **Casos de Prueba Recomendados**
1. Login con credenciales válidas
2. Login con email inválido
3. Login con contraseña muy corta
4. Login con campos vacíos
5. Manejo de token expirado
6. Renovación automática de token

---

## 📞 Soporte

Para dudas sobre la migración:
- **Documentación**: Este archivo
- **Tests**: `src/modules/auth/auth.service.spec.ts`
- **Configuración**: `src/config/auth.config.ts`
