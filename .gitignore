node_modules/
.git/
coverage/
*.pem
# compiled output
.env
/dist
/node_modules
tmp/
yarn.lock

.whatsapp-auth
# Logs
logs
*.log
npm-debug.log*
pnpm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*
.wwebjs_auth
.wwebjs_cache
# OS
.DS_Store
.whatsapp-auth.zip
whatsapp-auth.zip
# Tests
/coverage
/.nyc_output

# IDEs and editors
/.idea
.project
.classpath
.c9/
*.launch
.settings/
*.sublime-workspace

# IDE - VSCode
.vscode/*
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json
.vscode/

# Local Netlify folder
.netlify
