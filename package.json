{"name": "bot-real-estate", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "engines": {"node": ">=20.0.0"}, "scripts": {"build": "nest build", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\""}, "dependencies": {"@aws-sdk/client-s3": "^3.782.0", "@aws-sdk/lib-storage": "^3.782.0", "@aws-sdk/s3-request-presigner": "^3.782.0", "@nestjs/common": "^11.0.13", "@nestjs/config": "^4.0.2", "@nestjs/core": "^11.0.13", "@nestjs/event-emitter": "^3.0.1", "@nestjs/jwt": "^11.0.0", "@nestjs/passport": "^11.0.5", "@nestjs/platform-express": "^11.0.13", "@nestjs/platform-socket.io": "^11.0.13", "@nestjs/schedule": "^5.0.1", "@nestjs/swagger": "^11.1.1", "@nestjs/typeorm": "^11.0.0", "@nestjs/websockets": "^11.0.13", "@nestjsx/crud": "^5.0.0-alpha.3", "@nestjsx/crud-typeorm": "^5.0.0-alpha.3", "axios": "^1.8.4", "bcryptjs": "^3.0.2", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "cookie-parser": "^1.4.7", "dotenv": "^16.4.7", "fs": "^0.0.1-security", "google-auth-library": "^9.15.1", "googleapis": "^148.0.0", "image-size": "^2.0.2", "moment": "^2.30.1", "moment-timezone": "^0.5.48", "openai": "^4.91.1", "pg": "^8.14.1", "pm2": "^6.0.5", "puppeteer": "^24.6.0", "qrcode-terminal": "^0.12.0", "reflect-metadata": "^0.2.2", "resend": "^4.2.0", "rimraf": "^6.0.1", "rxjs": "^7.8.2", "socket.io": "^4.8.1", "stripe": "^18.0.0", "typeorm": "^0.3.22", "typeorm-seeding": "^1.6.1", "uuid": "^11.1.0", "whatsapp-web.js": "^1.27.0"}, "devDependencies": {"@nestjs/cli": "^11.0.6", "@nestjs/schematics": "^11.0.3", "@nestjs/testing": "^11.0.13", "@types/bcrypt": "^5.0.2", "@types/express": "^5.0.1", "@types/jest": "29.5.14", "@types/multer": "^1.4.12", "@types/node": "^22.14.0", "@types/nodemailer": "^6.4.17", "@types/passport-jwt": "^4.0.1", "@types/passport-local": "^1.0.38", "@types/supertest": "^6.0.3", "@typescript-eslint/eslint-plugin": "^8.29.0", "@typescript-eslint/parser": "^8.29.0", "eslint": "^9.24.0", "eslint-config-prettier": "^10.1.1", "eslint-plugin-prettier": "^5.2.6", "jest": "29.7.0", "prettier": "^3.5.3", "source-map-support": "^0.5.21", "supertest": "^7.1.0", "ts-jest": "29.3.1", "ts-loader": "^9.5.2", "ts-node": "^10.9.2", "tsconfig-paths": "4.2.0", "typescript": "^5.8.3"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}