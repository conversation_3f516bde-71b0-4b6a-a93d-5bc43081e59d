
# Etapa 1: Construir la aplicación
FROM node:20 AS builder

# Establece el directorio de trabajo
WORKDIR /app

# Copia los archivos necesarios
COPY package.json yarn.lock biitbot-google-auth.json ./
RUN yarn

COPY . .

# Construir la aplicación
RUN yarn build

# Etapa 2: Ejecutar la aplicación
FROM node:20

RUN apt-get update && apt-get install -y libnss3 --no-install-recommends && rm -rf /var/lib/apt/lists/*

RUN apt-get update && apt-get install -y \
  ffmpeg \
  nano \
  zip unzip \
  fonts-ipafont-gothic fonts-wqy-zenhei fonts-thai-tlwg fonts-kacst fonts-freefont-ttf \
  chromium \
  libnss3 \
  --no-install-recommends && rm -rf /var/lib/apt/lists/*

# Establece el directorio de trabajo
WORKDIR /app

# Copia los archivos construidos de la etapa 1
COPY --from=builder /app/package.json /app/yarn.lock /app/biitbot-google-auth.json ./
RUN yarn install --production

COPY --from=builder /app/dist ./dist

# Puerto expuesto
EXPOSE 3000

# Comando para ejecutar la app
CMD ["node", "dist/main"]
