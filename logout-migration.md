# 🔐 Logout API - Guía para Frontend

## 📋 **Resumen**

Esta guía describe cómo consumir los nuevos endpoints de logout desde el frontend. El sistema ahora implementa logout real que invalida tokens JWT inmediatamente.

---

## 🎯 **¿Qué Cambió?**

**Antes:** Los tokens JWT seguían siendo válidos después del logout hasta su expiración natural.

**Ahora:** Los tokens se invalidan inmediatamente al hacer logout y no pueden ser reutilizados.

---

## 🌐 **Endpoints Disponibles**

### **Base URL:** `http://localhost:3000` (desarrollo) | `https://api.biitbot.com` (producción)

---

## � **1. Logout Individual**

### **POST** `/auth/logout`

Invalida el token actual del usuario (cierra sesión en el dispositivo actual).

#### **Headers Requeridos:**
```javascript
{
  "Authorization": "Bearer <your-jwt-token>",
  "Content-Type": "application/json"
}
```

#### **Body:**
```javascript
// No requiere body - el token se extrae del header Authorization
{}
```

#### **Respuesta Exitosa (200):**
```javascript
{
  "message": "Logout successful"
}
```

#### **Respuesta de Error (401):**
```javascript
{
  "statusCode": 401,
  "message": "Token has been revoked", // Si el token ya estaba invalidado
  "error": "Unauthorized"
}
```

#### **Ejemplo con Fetch:**
```javascript
const logout = async () => {
  try {
    const token = localStorage.getItem('accessToken'); // o donde guardes el token

    const response = await fetch('/auth/logout', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });

    if (response.ok) {
      // Logout exitoso
      localStorage.removeItem('accessToken'); // Limpiar token del storage
      window.location.href = '/login'; // Redirigir al login
    } else {
      console.error('Error en logout:', await response.json());
    }
  } catch (error) {
    console.error('Error de red:', error);
  }
};
```

#### **Ejemplo con Axios:**
```javascript
import axios from 'axios';

const logout = async () => {
  try {
    const token = localStorage.getItem('accessToken');

    await axios.post('/auth/logout', {}, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });

    // Logout exitoso
    localStorage.removeItem('accessToken');
    window.location.href = '/login';
  } catch (error) {
    console.error('Error en logout:', error.response?.data);
  }
};
```

---

## 🔄 **2. Logout Global (Todas las Sesiones)**

### **POST** `/auth/logout-all`

Invalida TODOS los tokens del usuario (cierra sesión en todos los dispositivos).

#### **Headers Requeridos:**
```javascript
{
  "Authorization": "Bearer <your-jwt-token>",
  "Content-Type": "application/json"
}
```

#### **Body:**
```javascript
// No requiere body - el usuario se extrae del token
{}
```

#### **Respuesta Exitosa (200):**
```javascript
{
  "message": "All sessions logged out successfully",
  "tokensInvalidated": 1
}
```

#### **Respuesta de Error (401):**
```javascript
{
  "statusCode": 401,
  "message": "Token has been revoked",
  "error": "Unauthorized"
}
```

#### **Ejemplo con Fetch:**
```javascript
const logoutAll = async () => {
  try {
    const token = localStorage.getItem('accessToken');

    const response = await fetch('/auth/logout-all', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });

    if (response.ok) {
      const data = await response.json();
      console.log(`${data.tokensInvalidated} sesiones cerradas`);

      // Limpiar token local
      localStorage.removeItem('accessToken');
      window.location.href = '/login';
    } else {
      console.error('Error en logout global:', await response.json());
    }
  } catch (error) {
    console.error('Error de red:', error);
  }
};
```

#### **Ejemplo con Axios:**
```javascript
const logoutAll = async () => {
  try {
    const token = localStorage.getItem('accessToken');

    const response = await axios.post('/auth/logout-all', {}, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });

    console.log(`${response.data.tokensInvalidated} sesiones cerradas`);
    localStorage.removeItem('accessToken');
    window.location.href = '/login';
  } catch (error) {
    console.error('Error en logout global:', error.response?.data);
  }
};
```

---

## ⚠️ **Manejo de Errores**

### **Errores Comunes:**

#### **401 - Token Inválido o Expirado**
```javascript
{
  "statusCode": 401,
  "message": "Invalid token",
  "error": "Unauthorized"
}
```

#### **401 - Token Ya Revocado**
```javascript
{
  "statusCode": 401,
  "message": "Token has been revoked",
  "error": "Unauthorized"
}
```

#### **401 - Sin Token**
```javascript
{
  "statusCode": 401,
  "message": "No token provided",
  "error": "Unauthorized"
}
```

### **Manejo Recomendado:**
```javascript
const handleLogoutError = (error) => {
  if (error.response?.status === 401) {
    // Token ya inválido, limpiar storage y redirigir
    localStorage.removeItem('accessToken');
    window.location.href = '/login';
  } else {
    // Otro tipo de error
    console.error('Error inesperado:', error);
    // Mostrar mensaje al usuario
  }
};
```

---

## � **Flujo de Trabajo Recomendado**

### **1. Implementar Logout en tu App**

#### **Componente de Logout (React):**
```javascript
import { useState } from 'react';
import axios from 'axios';

const LogoutButton = () => {
  const [loading, setLoading] = useState(false);

  const handleLogout = async () => {
    setLoading(true);
    try {
      const token = localStorage.getItem('accessToken');

      await axios.post('/auth/logout', {}, {
        headers: { 'Authorization': `Bearer ${token}` }
      });

      // Limpiar estado local
      localStorage.removeItem('accessToken');
      // Redirigir o actualizar estado global
      window.location.href = '/login';
    } catch (error) {
      console.error('Error en logout:', error);
      // Incluso si falla, limpiar token local
      localStorage.removeItem('accessToken');
      window.location.href = '/login';
    } finally {
      setLoading(false);
    }
  };

  return (
    <button onClick={handleLogout} disabled={loading}>
      {loading ? 'Cerrando sesión...' : 'Cerrar Sesión'}
    </button>
  );
};
```

#### **Componente de Logout Global (React):**
```javascript
const LogoutAllButton = () => {
  const [loading, setLoading] = useState(false);

  const handleLogoutAll = async () => {
    const confirmed = window.confirm(
      '¿Cerrar sesión en todos los dispositivos? Esta acción no se puede deshacer.'
    );

    if (!confirmed) return;

    setLoading(true);
    try {
      const token = localStorage.getItem('accessToken');

      const response = await axios.post('/auth/logout-all', {}, {
        headers: { 'Authorization': `Bearer ${token}` }
      });

      alert(`${response.data.tokensInvalidated} sesiones cerradas exitosamente`);
      localStorage.removeItem('accessToken');
      window.location.href = '/login';
    } catch (error) {
      console.error('Error en logout global:', error);
      localStorage.removeItem('accessToken');
      window.location.href = '/login';
    } finally {
      setLoading(false);
    }
  };

  return (
    <button onClick={handleLogoutAll} disabled={loading} className="danger">
      {loading ? 'Cerrando todas las sesiones...' : 'Cerrar Todas las Sesiones'}
    </button>
  );
};
```

### **2. Interceptor de Axios (Recomendado)**

```javascript
// Configurar interceptor para manejar tokens revocados automáticamente
axios.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      const message = error.response.data?.message;

      if (message === 'Token has been revoked' || message === 'Invalid token') {
        // Token revocado, limpiar y redirigir
        localStorage.removeItem('accessToken');
        window.location.href = '/login';
      }
    }
    return Promise.reject(error);
  }
);
```

---

## 🧪 **Testing desde Frontend**

### **Flujo de Prueba Completo:**

1. **Login y obtener token**
2. **Hacer request protegido** (debe funcionar)
3. **Hacer logout**
4. **Intentar request protegido nuevamente** (debe fallar con 401)

```javascript
const testLogoutFlow = async () => {
  try {
    // 1. Login
    const loginResponse = await axios.post('/auth/login', {
      email: '<EMAIL>',
      password: 'password123'
    });

    const token = loginResponse.data.accessToken;
    localStorage.setItem('accessToken', token);
    console.log('✅ Login exitoso');

    // 2. Request protegido (debe funcionar)
    await axios.get('/employee', {
      headers: { 'Authorization': `Bearer ${token}` }
    });
    console.log('✅ Request protegido funcionó');

    // 3. Logout
    await axios.post('/auth/logout', {}, {
      headers: { 'Authorization': `Bearer ${token}` }
    });
    console.log('✅ Logout exitoso');

    // 4. Intentar request protegido (debe fallar)
    try {
      await axios.get('/employee', {
        headers: { 'Authorization': `Bearer ${token}` }
      });
      console.log('❌ ERROR: Request debería haber fallado');
    } catch (error) {
      if (error.response?.status === 401) {
        console.log('✅ Token correctamente invalidado');
      }
    }
  } catch (error) {
    console.error('Error en test:', error);
  }
};
```

---

## 🎯 **Casos de Uso**

### **Cuándo usar `/auth/logout`:**
- ✅ Botón "Cerrar Sesión" normal
- ✅ Logout automático por inactividad
- ✅ Cierre de sesión voluntario del usuario

### **Cuándo usar `/auth/logout-all`:**
- ✅ Cambio de contraseña
- ✅ Sospecha de cuenta comprometida
- ✅ Opción "Cerrar sesión en todos los dispositivos"
- ✅ Configuración de seguridad avanzada

---

## ✅ **Checklist de Implementación**

- [ ] Implementar botón de logout individual
- [ ] Implementar botón de logout global (opcional)
- [ ] Configurar interceptor de Axios para manejar tokens revocados
- [ ] Limpiar localStorage/sessionStorage al hacer logout
- [ ] Redirigir al login después del logout
- [ ] Manejar errores de red apropiadamente
- [ ] Mostrar loading states durante logout
- [ ] Confirmar logout global con el usuario
- [ ] Probar flujo completo de logout

**🎉 ¡Listo para implementar logout seguro en tu frontend!**
