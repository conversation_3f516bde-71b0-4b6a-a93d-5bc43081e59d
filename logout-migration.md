# 🔐 Logout Migration - Sistema de Blacklist de Tokens

## 📋 **Resumen de la Implementación**

Este documento describe la implementación completa del sistema de logout seguro con blacklist de tokens JWT usando PostgreSQL y TypeORM en el proyecto Biitbot.

---

## 🎯 **Objetivo**

Implementar un sistema de logout real que invalide tokens JWT inmediatamente, solucionando el problema de que los JWT son stateless y no pueden ser revocados hasta su expiración natural.

---

## 🗄️ **1. Entidad BlacklistedToken**

### **Archivo:** `src/modules/auth/blacklisted-token.entity.ts`

```typescript
@Entity('blacklisted_tokens')
export class BlacklistedToken {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ name: 'token_id', length: 64, unique: true })
  tokenId: string; // Últimos caracteres del token para identificación

  @Column({ name: 'user_id' })
  userId: number;

  @Column({ name: 'reason', length: 50, default: 'logout' })
  reason: string; // 'logout', 'logout_all', 'password_change', etc.

  @Column({ name: 'expires_at', type: 'timestamp' })
  expiresAt: Date; // Cuándo expiraría el token originalmente

  @CreateDateColumn({ name: 'blacklisted_at' })
  blacklistedAt: Date;

  // Relación con Employee
  @ManyToOne(() => Employee, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'user_id' })
  user: Employee;
}
```

### **Tabla Creada Automáticamente:**
```sql
CREATE TABLE "blacklisted_tokens" (
  "id" SERIAL PRIMARY KEY,
  "token_id" VARCHAR(64) UNIQUE NOT NULL,
  "user_id" INTEGER NOT NULL,
  "reason" VARCHAR(50) DEFAULT 'logout',
  "expires_at" TIMESTAMP NOT NULL,
  "blacklisted_at" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY ("user_id") REFERENCES "employee"("id") ON DELETE CASCADE
);
```

---

## 🔧 **2. AuthService - Métodos Agregados**

### **Archivo:** `src/modules/auth/auth.service.ts`

#### **2.1 Método blacklistToken()**
```typescript
async blacklistToken(token: string, reason: string = 'logout'): Promise<void> {
  const payload = this.jwtService.decode(token) as TOKEN_PAYLOAD;
  const tokenId = this.generateTokenId(token);
  const expiresAt = new Date(payload.exp * 1000);

  const blacklistedToken = new BlacklistedToken();
  blacklistedToken.tokenId = tokenId;
  blacklistedToken.userId = payload.sub;
  blacklistedToken.reason = reason;
  blacklistedToken.expiresAt = expiresAt;

  await this.blacklistedTokenRepo.save(blacklistedToken);
}
```

#### **2.2 Método isTokenBlacklisted()**
```typescript
async isTokenBlacklisted(token: string): Promise<boolean> {
  const tokenId = this.generateTokenId(token);
  const blacklistedToken = await this.blacklistedTokenRepo.findOne({
    where: { tokenId },
  });
  return blacklistedToken !== null;
}
```

#### **2.3 Método blacklistAllUserTokens()**
```typescript
async blacklistAllUserTokens(userId: number, reason: string = 'logout_all'): Promise<number> {
  // Implementa logout global para invalidar todas las sesiones del usuario
}
```

#### **2.4 Método cleanupExpiredBlacklistedTokens()**
```typescript
async cleanupExpiredBlacklistedTokens(): Promise<number> {
  // Elimina tokens expirados de la blacklist para mantenimiento
}
```

---

## 🛡️ **3. AuthGuard - Verificación de Blacklist**

### **Archivo:** `src/modules/auth/auth.guard.ts`

#### **Verificación en Dos Pasos:**
```typescript
async canActivate(context: ExecutionContext): Promise<boolean> {
  // 1. Verificar firma y expiración del JWT
  const payload = await this.jwtService.verifyAsync(token, {
    secret: process.env.JWT_SECRET_KEY,
  });

  // 2. Verificar si el token está en la blacklist
  const isBlacklisted = await this.authService.isTokenBlacklisted(token);
  if (isBlacklisted) {
    throw new UnauthorizedException('Token has been revoked');
  }

  request['user'] = payload;
  request['token'] = token;
  return true;
}
```

---

## 🌐 **4. Endpoints Implementados**

### **Archivo:** `src/modules/auth/auth.controller.ts`

#### **4.1 POST /auth/logout**
```typescript
@Post('logout')
@UseGuards(AuthGuard)
async logout(@Req() request: any) {
  const token = this.extractTokenFromRequest(request);
  if (token) {
    await this.authService.blacklistToken(token, 'logout');
  }
  return { message: 'Logout successful' };
}
```

#### **4.2 POST /auth/logout-all**
```typescript
@Post('logout-all')
@UseGuards(AuthGuard)
async logoutAll(@Req() request: any) {
  const user = request.user;
  const tokensInvalidated = await this.authService.blacklistAllUserTokens(user.sub, 'logout_all');
  return { 
    message: 'All sessions logged out successfully',
    tokensInvalidated 
  };
}
```

---

## ⚙️ **5. Configuración del Módulo**

### **Archivo:** `src/modules/auth/auth.module.ts`

#### **Cambios Realizados:**
```typescript
@Global() // ✅ Módulo global para acceso desde todos los módulos
@Module({
  imports: [
    // ... otros imports
    TypeOrmModule.forFeature([Auth, Company, BlacklistedToken]), // ✅ Entidad agregada
  ],
  providers: [AuthService], // ✅ AuthService disponible globalmente
  controllers: [AuthController],
  exports: [AuthService],
})
export class AuthModule {}
```

---

## 🔄 **6. Flujo de Funcionamiento**

### **6.1 Login Normal**
```
1. Usuario hace login → POST /auth/login
2. AuthService genera JWT token
3. Token se retorna al cliente
4. Cliente guarda token (localStorage, cookies, etc.)
```

### **6.2 Requests Protegidos**
```
1. Cliente envía request con token → Authorization: Bearer <token>
2. AuthGuard verifica:
   a. Firma del JWT ✅
   b. Expiración del JWT ✅
   c. Token NO está en blacklist ✅
3. Si todo OK → Request procede
4. Si token blacklisteado → 401 Unauthorized
```

### **6.3 Logout Individual**
```
1. Cliente hace logout → POST /auth/logout
2. AuthGuard verifica token (debe ser válido para hacer logout)
3. AuthService agrega token a blacklist
4. Token inmediatamente invalidado
5. Próximos requests con ese token → 401 Unauthorized
```

### **6.4 Logout Global**
```
1. Cliente hace logout global → POST /auth/logout-all
2. AuthService invalida TODOS los tokens del usuario
3. Usuario deslogueado de todos los dispositivos
4. Útil para: cambio de contraseña, sospecha de compromiso, etc.
```

---

## 📊 **7. Beneficios Logrados**

### **🔒 Seguridad**
- ✅ **Logout Real**: Tokens invalidados inmediatamente
- ✅ **Control Total**: Capacidad de revocar tokens específicos
- ✅ **Logout Global**: Cerrar todas las sesiones de un usuario
- ✅ **Auditoría**: Registro de todas las invalidaciones con razón

### **⚡ Performance**
- ✅ **Consultas Rápidas**: Índice único en `token_id`
- ✅ **Fallback Graceful**: Si BD falla, no rompe autenticación
- ✅ **Limpieza Automática**: Eliminación de tokens expirados

### **🔧 Operacional**
- ✅ **TypeORM Sync**: Tabla creada automáticamente
- ✅ **Módulo Global**: Sin conflictos de dependencias
- ✅ **Mantenimiento**: Método de limpieza incluido

---

## 🧪 **8. Testing**

### **8.1 Probar Logout Individual**
```bash
# 1. Login
curl -X POST http://localhost:3000/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>", "password": "password"}'

# 2. Usar token en request protegido (debe funcionar)
curl -X GET http://localhost:3000/employee \
  -H "Authorization: Bearer <token>"

# 3. Logout
curl -X POST http://localhost:3000/auth/logout \
  -H "Authorization: Bearer <token>"

# 4. Intentar usar token nuevamente (debe fallar con 401)
curl -X GET http://localhost:3000/employee \
  -H "Authorization: Bearer <token>"
```

### **8.2 Verificar en Base de Datos**
```sql
-- Ver tokens blacklisteados
SELECT * FROM blacklisted_tokens ORDER BY blacklisted_at DESC;

-- Ver tokens de un usuario específico
SELECT * FROM blacklisted_tokens WHERE user_id = 1;

-- Limpiar tokens expirados (mantenimiento)
DELETE FROM blacklisted_tokens WHERE expires_at < NOW();
```

---

## 🚀 **9. Próximos Pasos Sugeridos**

### **Fase 3: Refresh Tokens**
- Implementar tokens de acceso cortos (30 min) + refresh tokens largos (7 días)
- Endpoint `/auth/refresh` para renovación automática
- Rotación de refresh tokens para mayor seguridad

### **Mejoras Adicionales**
- Cron job para limpieza automática de tokens expirados
- Dashboard de sesiones activas para usuarios
- Notificaciones de login desde nuevos dispositivos
- Rate limiting en endpoints de autenticación

---

## ✅ **Estado Actual: COMPLETADO**

- ✅ Entidad BlacklistedToken creada
- ✅ AuthService con métodos de blacklist
- ✅ AuthGuard con verificación de blacklist
- ✅ Endpoints /auth/logout y /auth/logout-all
- ✅ AuthModule configurado como global
- ✅ Aplicación funcionando sin errores
- ✅ Tabla blacklisted_tokens creada automáticamente

**🎉 El sistema de logout seguro está completamente implementado y funcionando!**
