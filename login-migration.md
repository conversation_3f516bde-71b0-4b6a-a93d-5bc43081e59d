# 🔐 Login API - Guía para Frontend

## 📋 **Resumen**

Esta guía describe cómo consumir el endpoint de login desde el frontend, incluyendo validaciones, manejo de errores y mejores prácticas de implementación.

---

## 🎯 **¿Qué Cambió?**

**Antes:** Validaciones básicas y mensajes de error genéricos.

**Ahora:** Validaciones estrictas, mensajes específicos y mejor seguridad.

---

## 🌐 **Endpoint de Login**

### **Base URL:** `http://localhost:3000` (desarrollo) | `https://api.biitbot.com` (producción)

---

## � **POST /auth/login**

Autentica al usuario y retorna un token JWT para acceder a recursos protegidos.

### **Headers Requeridos:**
```javascript
{
  "Content-Type": "application/json"
}
```

### **Body Requerido:**
```javascript
{
  "email": "<EMAIL>",
  "password": "password123"
}
```

### **Validaciones del Body:**

#### **Email:**
- ✅ **Requerido**: No puede estar vacío
- ✅ **Formato válido**: Debe cumplir regex `/^[^\s@]+@[^\s@]+\.[^\s@]+$/`
- ✅ **Longitud**: Máximo 254 caracteres

#### **Password:**
- ✅ **Requerido**: No puede estar vacío
- ✅ **Longitud mínima**: 6 caracteres
- ✅ **Longitud máxima**: 100 caracteres

### **Respuesta Exitosa (200):**
```javascript
{
  "userData": {
    "id": 1,
    "name": "John Doe",
    "email": "<EMAIL>",
    "company": {
      "id": 1,
      "name": "Company Name"
    },
    "ability": [
      {
        "action": "manage",
        "subject": "all"
      }
    ]
  },
  "shouldUpdatePassword": false,
  "accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```

### **Respuestas de Error:**

#### **400 - Validación Fallida**
```javascript
{
  "statusCode": 400,
  "message": "Email and password are required",
  "error": "Bad Request"
}
```

#### **400 - Email Inválido**
```javascript
{
  "statusCode": 400,
  "message": "Please provide a valid email address",
  "error": "Bad Request"
}
```

#### **400 - Contraseña Muy Corta**
```javascript
{
  "statusCode": 400,
  "message": "Password must be at least 6 characters long",
  "error": "Bad Request"
}
```

#### **401 - Credenciales Incorrectas**
```javascript
{
  "statusCode": 401,
  "message": "Invalid credentials",
  "error": "Unauthorized"
}
```

#### **401 - Email No Confirmado**
```javascript
{
  "statusCode": 401,
  "message": "Email not confirmed. Please check your email and confirm your account",
  "error": "Unauthorized"
}
```

---

## � **Ejemplos de Implementación**

### **Ejemplo con Fetch:**
```javascript
const login = async (email, password) => {
  try {
    const response = await fetch('/auth/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        email: email,
        password: password
      })
    });

    if (response.ok) {
      const data = await response.json();

      // Guardar token
      localStorage.setItem('accessToken', data.accessToken);

      // Verificar si necesita cambiar contraseña
      if (data.shouldUpdatePassword) {
        window.location.href = '/change-password';
      } else {
        window.location.href = '/dashboard';
      }

      return data;
    } else {
      const errorData = await response.json();
      throw new Error(errorData.message);
    }
  } catch (error) {
    console.error('Error en login:', error.message);
    throw error;
  }
};
```

### **Ejemplo con Axios:**
```javascript
import axios from 'axios';

const login = async (email, password) => {
  try {
    const response = await axios.post('/auth/login', {
      email: email,
      password: password
    });

    const { userData, accessToken, shouldUpdatePassword } = response.data;

    // Guardar token
    localStorage.setItem('accessToken', accessToken);

    // Guardar datos del usuario (opcional)
    localStorage.setItem('userData', JSON.stringify(userData));

    // Verificar si necesita cambiar contraseña
    if (shouldUpdatePassword) {
      window.location.href = '/change-password';
    } else {
      window.location.href = '/dashboard';
    }

    return response.data;
  } catch (error) {
    const errorMessage = error.response?.data?.message || 'Error de conexión';
    console.error('Error en login:', errorMessage);
    throw new Error(errorMessage);
  }
};
```

---

## ⚠️ **Validaciones del Frontend**

### **Función de Validación Recomendada:**
```javascript
const validateLoginForm = (email, password) => {
  const errors = {};
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

  // Validar email
  if (!email) {
    errors.email = 'Email es requerido';
  } else if (!emailRegex.test(email)) {
    errors.email = 'Por favor ingrese un email válido';
  } else if (email.length > 254) {
    errors.email = 'Email demasiado largo';
  }

  // Validar contraseña
  if (!password) {
    errors.password = 'Contraseña es requerida';
  } else if (password.length < 6) {
    errors.password = 'La contraseña debe tener al menos 6 caracteres';
  } else if (password.length > 100) {
    errors.password = 'La contraseña no puede exceder 100 caracteres';
  }

  return {
    isValid: Object.keys(errors).length === 0,
    errors: errors
  };
};
```

### **Manejo de Errores de la API:**
```javascript
const handleLoginError = (error) => {
  const message = error.message || 'Error desconocido';

  switch (message) {
    case 'Email and password are required':
      return 'Por favor complete todos los campos';

    case 'Please provide a valid email address':
      return 'Por favor ingrese un email válido';

    case 'Password must be at least 6 characters long':
      return 'La contraseña debe tener al menos 6 caracteres';

    case 'Invalid credentials':
      return 'Email o contraseña incorrectos';

    case 'Email not confirmed. Please check your email and confirm your account':
      return 'Por favor confirme su email antes de iniciar sesión';

    default:
      return 'Error de autenticación. Intente nuevamente';
  }
};
```

---

## ⚛️ **Componente React Completo**

### **LoginForm.jsx:**
```javascript
import React, { useState } from 'react';
import axios from 'axios';

const LoginForm = () => {
  const [formData, setFormData] = useState({ email: '', password: '' });
  const [errors, setErrors] = useState({});
  const [loading, setLoading] = useState(false);

  const validateForm = () => {
    const validation = validateLoginForm(formData.email, formData.password);
    setErrors(validation.errors);
    return validation.isValid;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!validateForm()) return;

    setLoading(true);
    try {
      const response = await axios.post('/auth/login', {
        email: formData.email,
        password: formData.password
      });

      const { userData, accessToken, shouldUpdatePassword } = response.data;

      // Guardar token y datos del usuario
      localStorage.setItem('accessToken', accessToken);
      localStorage.setItem('userData', JSON.stringify(userData));

      // Verificar si necesita actualizar contraseña
      if (shouldUpdatePassword) {
        window.location.href = '/change-password';
      } else {
        window.location.href = '/dashboard';
      }

    } catch (error) {
      const errorMessage = error.response?.data?.message || 'Error de conexión';
      setErrors({ general: handleLoginError({ message: errorMessage }) });
    } finally {
      setLoading(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="login-form">
      <div className="form-group">
        <input
          type="email"
          placeholder="Email"
          value={formData.email}
          onChange={(e) => setFormData({...formData, email: e.target.value})}
          maxLength={254}
          className={errors.email ? 'error' : ''}
        />
        {errors.email && <span className="error-message">{errors.email}</span>}
      </div>

      <div className="form-group">
        <input
          type="password"
          placeholder="Contraseña"
          value={formData.password}
          onChange={(e) => setFormData({...formData, password: e.target.value})}
          minLength={6}
          maxLength={100}
          className={errors.password ? 'error' : ''}
        />
        {errors.password && <span className="error-message">{errors.password}</span>}
      </div>

      {errors.general && <div className="error-message general">{errors.general}</div>}

      <button type="submit" disabled={loading} className="login-button">
        {loading ? 'Iniciando sesión...' : 'Iniciar Sesión'}
      </button>
    </form>
  );
};

export default LoginForm;
```

### **CSS Recomendado:**
```css
.login-form {
  max-width: 400px;
  margin: 0 auto;
  padding: 20px;
}

.form-group {
  margin-bottom: 15px;
}

.form-group input {
  width: 100%;
  padding: 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 16px;
}

.form-group input.error {
  border-color: #e74c3c;
}

.error-message {
  color: #e74c3c;
  font-size: 14px;
  margin-top: 5px;
  display: block;
}

.error-message.general {
  background-color: #fdf2f2;
  border: 1px solid #e74c3c;
  padding: 10px;
  border-radius: 4px;
  margin-bottom: 15px;
}

.login-button {
  width: 100%;
  padding: 12px;
  background-color: #3498db;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 16px;
  cursor: pointer;
}

.login-button:disabled {
  background-color: #bdc3c7;
  cursor: not-allowed;
}

.login-button:hover:not(:disabled) {
  background-color: #2980b9;
}
```

---

## � **Interceptor de Axios (Recomendado)**

### **Configuración Global:**
```javascript
import axios from 'axios';

// Configurar interceptor para manejar tokens automáticamente
axios.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('accessToken');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => Promise.reject(error)
);

// Interceptor para manejar respuestas y errores
axios.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      // Token expirado o inválido
      localStorage.removeItem('accessToken');
      localStorage.removeItem('userData');
      window.location.href = '/login';
    }

    if (error.response?.status === 400) {
      // Error de validación - el componente manejará el error específico
      console.log('Validation error:', error.response.data.message);
    }

    return Promise.reject(error);
  }
);
```

---

## 🧪 **Testing desde Frontend**

### **Flujo de Prueba Completo:**
```javascript
const testLoginFlow = async () => {
  try {
    // 1. Probar login exitoso
    const loginData = await login('<EMAIL>', 'password123');
    console.log('✅ Login exitoso:', loginData.userData.name);

    // 2. Verificar que el token se guardó
    const savedToken = localStorage.getItem('accessToken');
    console.log('✅ Token guardado:', savedToken ? 'Sí' : 'No');

    // 3. Probar request protegido
    const response = await axios.get('/employee');
    console.log('✅ Request protegido funcionó');

  } catch (error) {
    console.error('❌ Error en test:', error.message);
  }
};

// Probar validaciones
const testValidations = () => {
  // Email inválido
  try {
    const result = validateLoginForm('invalid-email', 'password123');
    console.log('Email inválido:', result.errors.email);
  } catch (error) {
    console.log('Error esperado:', error.message);
  }

  // Contraseña muy corta
  try {
    const result = validateLoginForm('<EMAIL>', '123');
    console.log('Contraseña corta:', result.errors.password);
  } catch (error) {
    console.log('Error esperado:', error.message);
  }
};
```

---

## 🎯 **Casos de Uso**

### **Flujo Normal de Login:**
1. Usuario ingresa email y contraseña
2. Frontend valida formato antes de enviar
3. Se envía request a `/auth/login`
4. Backend valida credenciales
5. Se retorna token y datos del usuario
6. Frontend guarda token y redirige

### **Manejo de shouldUpdatePassword:**
```javascript
// Si shouldUpdatePassword es true
if (data.shouldUpdatePassword) {
  // Mostrar modal o redirigir a cambio de contraseña
  showPasswordUpdateModal();
  // o
  window.location.href = '/change-password';
}
```

### **Manejo de Email No Confirmado:**
```javascript
// Si el error es email no confirmado
if (error.message.includes('Email not confirmed')) {
  // Mostrar opción para reenviar email de confirmación
  showEmailConfirmationPrompt(formData.email);
}
```

---

## ✅ **Checklist de Implementación**

### **Frontend:**
- [ ] Implementar validaciones de formulario
- [ ] Manejar todos los códigos de error de la API
- [ ] Configurar interceptor de Axios
- [ ] Guardar token en localStorage/sessionStorage
- [ ] Implementar redirección después del login
- [ ] Manejar caso de shouldUpdatePassword
- [ ] Agregar loading states
- [ ] Implementar manejo de email no confirmado
- [ ] Probar todos los casos de error
- [ ] Verificar que el token se envía en requests protegidos

### **Seguridad:**
- [ ] Validar datos en el frontend antes de enviar
- [ ] No mostrar contraseñas en logs
- [ ] Limpiar tokens al hacer logout
- [ ] Manejar tokens expirados apropiadamente
- [ ] Usar HTTPS en producción

---

## 🚀 **Próximos Pasos**

### **Mejoras Futuras:**
1. **Refresh Tokens** - Renovación automática de tokens
2. **Remember Me** - Tokens de larga duración opcionales
3. **2FA** - Autenticación de dos factores
4. **Social Login** - Login con Google, Facebook, etc.
5. **Rate Limiting** - Protección contra ataques de fuerza bruta

### **Optimizaciones:**
- Implementar cache de datos del usuario
- Agregar animaciones de loading
- Mejorar UX de mensajes de error
- Implementar logout automático por inactividad

**🎉 ¡Listo para implementar login seguro en tu frontend!**
